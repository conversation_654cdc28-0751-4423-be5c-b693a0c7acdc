import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { FetchProviderClientListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-list/fetch-provider-client-list.use-case';
import { FetchProviderVisitListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/fetch-provider-visit-list.use-case';
import ':plugins/db';

// Do not forget to delete the file after the task is completed !!
@singleton()
class TheForkApiTestTask {
    constructor(
        private readonly _fetchProviderClientListUseCase: FetchProviderClientListUseCase,
        private readonly _fetchProviderVisitListUseCase: FetchProviderVisitListUseCase
    ) {}

    async execute(): Promise<void> {
        const startDate = new Date('2024-11-01');
        const endDate = new Date('2025-05-10');
        const restaurantId = '';
        const platformKey = PlatformKey.LAFOURCHETTE;

        // Test fetch client list
        const clients = await this._fetchProviderClientListUseCase.execute(startDate, endDate, restaurantId, platformKey);
        console.log('clients', clients);

        // Test fetch reservation list
        const visits = await this._fetchProviderVisitListUseCase.execute(startDate, endDate, restaurantId, platformKey);
        console.log('visits', visits);
    }
}

const task = container.resolve(TheForkApiTestTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
