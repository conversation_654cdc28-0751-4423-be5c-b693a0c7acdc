import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import {
    DbId,
    EntityRepository,
    IAggregatedWheelOfFortune,
    IAiInteraction,
    IAttribute,
    IAutomation,
    IBrick,
    IBrickGenerator,
    ICalendarEvent,
    ICampaign,
    ICategory,
    IClient,
    IComment,
    ICommentMention,
    IConversation,
    ICredential,
    IDiagnostic,
    IFacebookCredential,
    IFeedback,
    IFolder,
    IGeoSample,
    IGift,
    IGiftDraw,
    IGiftStock,
    IGmbCredential,
    IHashtag,
    IHoursType,
    IInformationUpdate,
    IIntelligentSubjectAutomation,
    IKeywordSearchImpressions,
    IKeywordTemp,
    ILabel,
    IMapsPlaceInfo,
    IMedia,
    IMention,
    IMessage,
    INfc,
    INotification,
    IOrganization,
    IPlatform,
    IPlatformInsight,
    IPost,
    IPrivateReview,
    IProviderClient,
    IPushNotification,
    IReport,
    IRestaurant,
    IRestaurantAiSettings,
    IRestaurantAttribute,
    IRestaurantKeyword,
    IRestaurantWheelOfFortune,
    IReview,
    IReviewAnalysis,
    IReviewReplyAutomation,
    IRoiInsights,
    IRoiSettings,
    IScan,
    ISegmentAnalysis,
    ISegmentAnalysisParentTopics,
    ISevenroomsCredential,
    ISimilarRestaurants,
    ISticker,
    IStoreLocatorOrganizationConfig,
    ITemplate,
    ITotem,
    ITranslations,
    IUser,
    IUserFilters,
    IUserRestaurant,
    IWeeklySearchRanking,
    IWheelOfFortune,
    IWheelOfFortuneSnapshot,
    IYextAccount,
    IYextLocation,
} from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { DeepPartial } from ':helpers/types/deep-partial';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { AttributesRepository } from ':modules/attributes/attributes.repository';
import AutomationsRepository from ':modules/automations/automations.repository';
import IntelligentSubjectAutomationsRepository from ':modules/automations/features/intelligent-subjects/repositories/intelligent-subjects.repository';
import ReviewReplyAutomationsRepository from ':modules/automations/features/review-replies/review-replies.repository';
import { BricksGeneratorRepository } from ':modules/brick-generators/brick-generators.repository';
import { BricksRepository } from ':modules/bricks/bricks.repository';
import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import CampaignsRepository from ':modules/campaigns/campaigns.repository';
import CategoriesRepository from ':modules/categories/categories.repository';
import ClientsRepository from ':modules/clients/clients.repository';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { CommentsRepository } from ':modules/comments/comments.repository';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import { GmbCredentialsRepository } from ':modules/credentials/platforms/gmb/gmb.repository';
import { SevenroomsCredentialRepository } from ':modules/credentials/platforms/sevenrooms/sevenrooms.repository';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import FeedbacksRepository from ':modules/feedbacks/feedback.repository';
import { FoldersRepository } from ':modules/folders/folders.repository';
import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';
import { GiftsRepository } from ':modules/gifts/gifts.repository';
import { GiftStocksRepository } from ':modules/gifts/stocks/gift-stocks.repository';
import { HashtagsRepository } from ':modules/hashtags/hashtags.repository';
import { HourTypesRepository } from ':modules/hour-types/hour-types.repository';
import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';
import { KeywordsTempRepository } from ':modules/keywords/keywords-temp.repository';
import { MapsPlaceInfoRepository } from ':modules/keywords/maps-place-info.repository';
import GeoSampleRepository from ':modules/keywords/modules/geolocation/geolocation.repository';
import { WeeklySearchRankingRepository } from ':modules/keywords/weekly-search-rankings.repository';
import LabelsRepository from ':modules/labels/labels.repository';
import { MediasRepository } from ':modules/media/medias.repository';
import CommentMentionsRepository from ':modules/mentions/commentMentions/comment-mentions.repository';
import MentionsRepository from ':modules/mentions/mentions.repository';
import { ConversationsRepository, MessagesRepository } from ':modules/messages/messages.repository';
import NfcsRepository from ':modules/nfc/nfcs.repository';
import { StickersRepository } from ':modules/nfc/stickers/stickers.repository';
import { TotemsRepository } from ':modules/nfc/totems/totems.repository';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsRepository from ':modules/posts/posts.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { YextAccountRepository } from ':modules/publishers/yext/repositories/yext-account.repository';
import { YextLocationRepository } from ':modules/publishers/yext/repositories/yext-location.repository';
import { PushNotificationsRepository } from ':modules/push-notifications/push-notifications.repository';
import ReportsRepository from ':modules/reports/reports.repository';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewAnalysesRepository } from ':modules/review-analyses/review-analyses.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { RoiInsightsRepository } from ':modules/roi-insights/roi-insights.repository';
import { RoiSettingsRepository } from ':modules/roi-settings/roi-settings.repository';
import ScansRepository from ':modules/scans/scans.repository';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';
import { SimilarRestaurantsRepository } from ':modules/similar-restaurants/similar-restaurants.repository';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import TemplatesRepository from ':modules/templates/templates.repository';
import { TranslationsRepository } from ':modules/translations/translations.repository';
import UserFiltersRepository from ':modules/user-filters/repositories/user-filters.repository';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { WheelOfFortuneSnapshotsRepository } from ':modules/wheel-of-fortune-snapshots/wheel-of-fortune-snapshots.repository';
import { AggregatedWheelsOfFortuneRepository } from ':modules/wheels-of-fortune/aggregated-wheels-of-fortune/aggregated-wheels-of-fortune.repository';
import { RestaurantWheelsOfFortuneRepository } from ':modules/wheels-of-fortune/restaurant-wheels-of-fortune/restaurant-wheels-of-fortune.repository';
import { WheelsOfFortuneRepository } from ':modules/wheels-of-fortune/wheels-of-fortune.repository';
import { Cache } from ':plugins/cache';
import { MongoCache } from ':plugins/mongo-cache';

const repositories = {
    AggregatedWheelsOfFortuneRepository: new AggregatedWheelsOfFortuneRepository(),
    AiInteractionsRepository: new AiInteractionsRepository(),
    AttributesRepository: new AttributesRepository(),
    AutomationsRepository: new AutomationsRepository(),
    BricksGeneratorRepository: new BricksGeneratorRepository(),
    BricksRepository: new BricksRepository(container.resolve(BricksGeneratorRepository)),
    CalendarEventsRepository: new CalendarEventsRepository(),
    CampaignsRepository: new CampaignsRepository(),
    CategoriesRepository: new CategoriesRepository(),
    ClientsRepository: new ClientsRepository(),
    CommentMentionsRepository: new CommentMentionsRepository(),
    CommentsRepository: new CommentsRepository(),
    ConversationsRepository: new ConversationsRepository(),
    CredentialsRepository: new CredentialsRepository(),
    DiagnosticsRepository: new DiagnosticsRepository(),
    FacebookCredentialsRepository: new FacebookCredentialsRepository(),
    FeedbacksRepository: new FeedbacksRepository(),
    FoldersRepository: new FoldersRepository(new MediasRepository()),
    GeoSampleRepository: new GeoSampleRepository(),
    GiftDrawsRepository: new GiftDrawsRepository(),
    GiftsRepository: new GiftsRepository(),
    GiftStocksRepository: new GiftStocksRepository(),
    GmbCredentialsRepository: new GmbCredentialsRepository(),
    HashtagsRepository: new HashtagsRepository(),
    HourTypesRepository: new HourTypesRepository(),
    InformationUpdatesRepository: new InformationUpdatesRepository(),
    IntelligentSubjectAutomationsRepository: new IntelligentSubjectAutomationsRepository(),
    KeywordSearchImpressionsRepository: new KeywordSearchImpressionsRepository(),
    KeywordsTempRepository: new KeywordsTempRepository(new TranslationsRepository()),
    LabelsRepository: new LabelsRepository(),
    MapsPlaceInfoRepository: new MapsPlaceInfoRepository(),
    MediasRepository: new MediasRepository(),
    MentionsRepository: new MentionsRepository(),
    MessagesRepository: new MessagesRepository(),
    NfcsRepository: new NfcsRepository(new PlatformsRepository()),
    NotificationsRepository: new NotificationsRepository(),
    OrganizationsRepository: new OrganizationsRepository(),
    PlatformInsightsRepository: new PlatformInsightsRepository(new PlatformsRepository()),
    PlatformsRepository: new PlatformsRepository(),
    PostsRepository: new PostsRepository(),
    PrivateReviewsRepository: new PrivateReviewsRepository(),
    ProviderClientsRepository: new ProviderClientsRepository(),
    PushNotificationsRepository: new PushNotificationsRepository(),
    ReportsRepository: container.resolve(ReportsRepository),
    RestaurantAiSettingsRepository: new RestaurantAiSettingsRepository(),
    RestaurantAttributesRepository: new RestaurantAttributesRepository(),
    RestaurantKeywordsRepository: new RestaurantKeywordsRepository(container.resolve(KeywordsTempRepository)),
    RestaurantsRepository: new RestaurantsRepository(),
    RestaurantWheelsOfFortuneRepository: new RestaurantWheelsOfFortuneRepository(),
    ReviewAnalysesRepository: new ReviewAnalysesRepository(),
    ReviewReplyAutomationsRepository: new ReviewReplyAutomationsRepository(),
    ReviewsRepository: new ReviewsRepository(container.resolve(PrivateReviewsRepository)),
    RoiInsightsRepository: new RoiInsightsRepository(),
    RoiSettingsRepository: new RoiSettingsRepository(),
    ScansRepository: new ScansRepository(),
    SegmentAnalysesRepository: new SegmentAnalysesRepository(),
    SegmentAnalysisParentTopicsRepository: new SegmentAnalysisParentTopicsRepository(),
    SevenroomsCredentialsRepository: new SevenroomsCredentialRepository(),
    SimilarRestaurantsRepository: new SimilarRestaurantsRepository(),
    StickersRepository: new StickersRepository(new NfcsRepository(new PlatformsRepository())),
    StoreLocatorOrganizationConfigRepository: new StoreLocatorOrganizationConfigRepository(),
    TemplatesRepository: new TemplatesRepository(),
    TotemsRepository: new TotemsRepository(new NfcsRepository(new PlatformsRepository())),
    TranslationsRepository: container.resolve(TranslationsRepository),
    UserFiltersRepository: new UserFiltersRepository(),
    UserRestaurantsRepository: new UserRestaurantsRepository(),
    UsersRepository: new UsersRepository(),
    WeeklySearchRankingRepository: new WeeklySearchRankingRepository(),
    WheelOfFortuneSnapshotsRepository: new WheelOfFortuneSnapshotsRepository(),
    WheelsOfFortuneRepository: new WheelsOfFortuneRepository(),
    YextAccountRepository: new YextAccountRepository(),
    YextLocationRepository: new YextLocationRepository(),
};
type Models = {
    aggregatedWheelsOfFortune: IAggregatedWheelOfFortune;
    aiInteractions: IAiInteraction;
    attributes: IAttribute;
    automations: IAutomation;
    bricks: IBrick;
    bricksgenerator: IBrickGenerator;
    calendarEvents: ICalendarEvent;
    campaigns: ICampaign;
    categories: ICategory;
    clients: IClient;
    commentMentions: ICommentMention;
    comments: IComment;
    conversations: IConversation;
    credentials: ICredential;
    diagnostics: IDiagnostic;
    facebookCredentials: IFacebookCredential;
    feedbacks: IFeedback;
    folders: IFolder;
    geoSamples: IGeoSample;
    giftDraws: IGiftDraw;
    gifts: IGift;
    giftStocks: IGiftStock;
    gmbCredentials: IGmbCredential;
    hashtags: IHashtag;
    hoursTypes: IHoursType;
    informationUpdates: IInformationUpdate;
    intelligentSubjectAutomations: IIntelligentSubjectAutomation;
    keywordSearchImpressions: IKeywordSearchImpressions;
    keywordsTemp: IKeywordTemp;
    labels: ILabel;
    mapsPlaceInfo: IMapsPlaceInfo;
    medias: IMedia;
    mentions: IMention;
    messages: IMessage;
    nfcs: INfc;
    notifications: INotification;
    organizations: IOrganization;
    platformInsights: IPlatformInsight;
    platforms: IPlatform;
    posts: IPost;
    privateReviews: IPrivateReview;
    providerClients: IProviderClient;
    pushNotifications: IPushNotification;
    reports: IReport;
    restaurantAiSettings: IRestaurantAiSettings;
    restaurantAttributes: IRestaurantAttribute;
    restaurantKeywords: IRestaurantKeyword;
    restaurants: IRestaurant;
    restaurantWheelsOfFortune: IRestaurantWheelOfFortune;
    reviewAnalysis: IReviewAnalysis;
    reviewReplyAutomations: IReviewReplyAutomation;
    reviews: IReview;
    roiInsights: IRoiInsights;
    roiSettings: IRoiSettings;
    scans: IScan;
    segmentAnalyses: ISegmentAnalysis;
    segmentAnalysisParentTopics: ISegmentAnalysisParentTopics;
    storeLocatorOrganizationConfigs: IStoreLocatorOrganizationConfig;
    sevenroomsCredentials: ISevenroomsCredential;
    similarRestaurants: ISimilarRestaurants;
    stickers: ISticker;
    templates: ITemplate;
    totems: ITotem;
    translations: ITranslations;
    userFilters: IUserFilters;
    userRestaurants: IUserRestaurant;
    users: IUser;
    weeklySearchRankings: IWeeklySearchRanking;
    wheelOfFortuneSnapshots: IWheelOfFortuneSnapshot;
    wheelsOfFortune: IWheelOfFortune;
    yextAccounts: IYextAccount;
    yextLocations: IYextLocation;
};

const _modelToRepository: {
    [key in keyof Models]: keyof typeof repositories;
} = {
    aggregatedWheelsOfFortune: 'AggregatedWheelsOfFortuneRepository',
    aiInteractions: 'AiInteractionsRepository',
    attributes: 'AttributesRepository',
    automations: 'AutomationsRepository',
    bricks: 'BricksRepository',
    bricksgenerator: 'BricksGeneratorRepository',
    calendarEvents: 'CalendarEventsRepository',
    campaigns: 'CampaignsRepository',
    categories: 'CategoriesRepository',
    clients: 'ClientsRepository',
    commentMentions: 'CommentMentionsRepository',
    comments: 'CommentsRepository',
    conversations: 'ConversationsRepository',
    credentials: 'CredentialsRepository',
    diagnostics: 'DiagnosticsRepository',
    facebookCredentials: 'FacebookCredentialsRepository',
    feedbacks: 'FeedbacksRepository',
    folders: 'FoldersRepository',
    geoSamples: 'GeoSampleRepository',
    giftDraws: 'GiftDrawsRepository',
    gifts: 'GiftsRepository',
    giftStocks: 'GiftStocksRepository',
    gmbCredentials: 'GmbCredentialsRepository',
    hashtags: 'HashtagsRepository',
    hoursTypes: 'HourTypesRepository',
    informationUpdates: 'InformationUpdatesRepository',
    intelligentSubjectAutomations: 'IntelligentSubjectAutomationsRepository',
    keywordSearchImpressions: 'KeywordSearchImpressionsRepository',
    keywordsTemp: 'KeywordsTempRepository',
    labels: 'LabelsRepository',
    mapsPlaceInfo: 'MapsPlaceInfoRepository',
    medias: 'MediasRepository',
    mentions: 'MentionsRepository',
    messages: 'MessagesRepository',
    nfcs: 'NfcsRepository',
    notifications: 'NotificationsRepository',
    organizations: 'OrganizationsRepository',
    platformInsights: 'PlatformInsightsRepository',
    platforms: 'PlatformsRepository',
    posts: 'PostsRepository',
    privateReviews: 'PrivateReviewsRepository',
    providerClients: 'ProviderClientsRepository',
    pushNotifications: 'PushNotificationsRepository',
    reports: 'ReportsRepository',
    restaurantAiSettings: 'RestaurantAiSettingsRepository',
    restaurantAttributes: 'RestaurantAttributesRepository',
    restaurantKeywords: 'RestaurantKeywordsRepository',
    restaurants: 'RestaurantsRepository',
    restaurantWheelsOfFortune: 'RestaurantWheelsOfFortuneRepository',
    reviewAnalysis: 'ReviewAnalysesRepository',
    reviewReplyAutomations: 'ReviewReplyAutomationsRepository',
    reviews: 'ReviewsRepository',
    roiInsights: 'RoiInsightsRepository',
    roiSettings: 'RoiSettingsRepository',
    scans: 'ScansRepository',
    segmentAnalyses: 'SegmentAnalysesRepository',
    segmentAnalysisParentTopics: 'SegmentAnalysisParentTopicsRepository',
    sevenroomsCredentials: 'SevenroomsCredentialsRepository',
    similarRestaurants: 'SimilarRestaurantsRepository',
    stickers: 'StickersRepository',
    storeLocatorOrganizationConfigs: 'StoreLocatorOrganizationConfigRepository',
    templates: 'TemplatesRepository',
    totems: 'TotemsRepository',
    translations: 'TranslationsRepository',
    userFilters: 'UserFiltersRepository',
    userRestaurants: 'UserRestaurantsRepository',
    users: 'UsersRepository',
    weeklySearchRankings: 'WeeklySearchRankingRepository',
    wheelOfFortuneSnapshots: 'WheelOfFortuneSnapshotsRepository',
    wheelsOfFortune: 'WheelsOfFortuneRepository',
    yextAccounts: 'YextAccountRepository',
    yextLocations: 'YextLocationRepository',
};

type RepositoryToken = keyof typeof repositories;

export const registerRepositories = (tokens: RepositoryToken[]) => {
    tokens.forEach((token) => {
        container.register(token, { useValue: repositories[token] });
    });
};

export const registerOtherDependencies = () => {
    container.register<Cache>(InjectionToken.Cache, {
        useClass: MongoCache,
    });
};

type Seed<Entity extends Models[keyof Models]> = DeepPartial<Omit<Entity, '_id'>> & { _id: DbId };

type DependencyResolvers<DependencyKeys extends keyof Models> = { [K in keyof Pick<Models, DependencyKeys>]: () => Models[K][] };

export type SeedPayloadV2<DependencyKeys extends keyof Models, ModelKey extends DependencyKeys> = {
    data: (dependencies: Omit<DependencyResolvers<DependencyKeys>, ModelKey>) => Seed<Models[ModelKey]>[];
};

export type TestCaseV2<DependencyKeys extends keyof Models> = {
    seeds: { [ModelKey in keyof Pick<Models, DependencyKeys>]: SeedPayloadV2<DependencyKeys, ModelKey> };
    expectedResult?: (dependencies: SeededObjects<DependencyKeys>) => any;
    expectedErrorCode?: MalouErrorCode;
};

type ExpectedResult<ModelKey extends keyof Models, F = TestCaseV2<ModelKey>['expectedResult']> = F extends (...args: any) => any
    ? ReturnType<F>
    : undefined;

type SeededObjects<ModelKey extends keyof Models> = { [K in keyof Pick<Models, ModelKey>]: Models[K][] };

class UnresolvedDependencyError extends Error {}

export class TestCaseBuilderV2<ModelKey extends keyof Models> {
    // set by the build() method
    private _seededObjects?: SeededObjects<ModelKey>;

    private _expectedResult?: ExpectedResult<ModelKey>;
    private _expectedErrorCode?: MalouErrorCode;
    private _cases: TestCaseV2<ModelKey>;

    constructor(_cases: TestCaseV2<ModelKey>) {
        this._cases = _cases;
    }

    public getExpectedResult(): ExpectedResult<ModelKey> | undefined {
        return this._expectedResult;
    }

    public getExpectedErrorCode(): MalouErrorCode | undefined {
        return this._expectedErrorCode;
    }

    public getSeededObjects(): SeededObjects<ModelKey> {
        assert(this._seededObjects, 'you must call build() before getSeededObjects()');
        for (const modelKey in this._seededObjects) {
            assert(this._seededObjects[modelKey]);
        }
        return this._seededObjects as SeededObjects<ModelKey>;
    }

    public build = async () => {
        await this._applyIndexes();
        await this._seedRepositories();
        assert(this._seededObjects);
        this._expectedResult = this._cases.expectedResult?.(this._seededObjects);
        this._expectedErrorCode = this._cases.expectedErrorCode;
    };

    private _seedRepositories = async (): Promise<void> => {
        const dummyDependencyFunction = () => {
            throw new UnresolvedDependencyError('missing dependency');
        };

        const dependencyResolvers: DependencyResolvers<ModelKey> = Object.keys(this._cases.seeds).reduce(
            (acc, key) => ({ ...acc, [key]: dummyDependencyFunction }),
            {} as any
        );

        while (Object.values(dependencyResolvers).some((seededObject) => seededObject === dummyDependencyFunction)) {
            let numberOfResolvedRepos = 0;
            for (const keyString in this._cases.seeds) {
                const key: ModelKey = keyString as unknown as ModelKey;
                if (this._cases.seeds.hasOwnProperty(key) && dependencyResolvers[key] === dummyDependencyFunction) {
                    const seed = this._cases.seeds[key];
                    const seeded = await this._seedRepository(seed, dependencyResolvers, key);
                    if (seeded) {
                        dependencyResolvers[key] = () => seeded;
                        numberOfResolvedRepos++;
                    }
                }
            }
            assert(
                numberOfResolvedRepos > 0,
                'Impossible to resolve dependencies, probably because there’s a circular dependency between some seed generators.'
            );
        }
        this._seededObjects = Object.fromEntries(
            Object.entries(dependencyResolvers).map(([key, valueGetter]) => [key, (valueGetter as any)()])
        ) as SeededObjects<ModelKey>;
    };

    /**
     * ModelKey is the union of many model keys (for instance something like `'restaurants' | 'users' | 'reviews'`)
     *
     * K is supposed to be one member of this union.
     */
    private _seedRepository = async <K extends ModelKey>(
        payload: SeedPayloadV2<ModelKey, K>,
        dependencyResolvers: DependencyResolvers<ModelKey>,
        modelName: string
    ) => {
        if (!payload || !payload.data) {
            return;
        }

        let seeds: Seed<Models[K]>[];
        try {
            seeds = payload.data(dependencyResolvers);
        } catch (error) {
            if (error instanceof UnresolvedDependencyError) {
                return undefined;
            }
            throw error;
        }

        const repositoryToken = _modelToRepository[modelName];
        const repository = container.resolve(repositoryToken) as EntityRepository<any>;

        const documents = await repository.createMany({ data: seeds, options: { lean: true } });
        return this._cleanDocuments(documents);
    };

    // Remove unwanted properties that Mongoose added when applying .toObject to a document (inside createMany method)
    private _cleanDocuments = (documents: any) => {
        return documents.map((document: any) => {
            const { id: _id, ...rest } = document;

            return rest;
        });
    };

    private _applyIndexes = async () => {
        const repoList = Object.keys(this._cases.seeds).map((collectionName) => {
            const repositoryToken = _modelToRepository[collectionName as ModelKey];
            return repositories[repositoryToken];
        });
        await Promise.all(
            repoList.map(async (repo) => {
                try {
                    if (repo && repo.model && typeof repo.model.syncIndexes === 'function') {
                        await repo.model.syncIndexes();
                    }
                } catch (err) {
                    const repoName = repo?.constructor?.name || 'UnknownRepository';
                    console.error(`[syncIndexes] Error in ${repoName}:`, err);
                }
            })
        );
    };
}
