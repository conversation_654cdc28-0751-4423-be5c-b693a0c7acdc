import { container } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { HandleTheForkUpsertedCustomerUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-customer/handle-thefork-upserted-customer.use-case';
import { HandleTheForkUpsertedReservationUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-reservation/handle-thefork-upserted-reservation.use-case';
import { HandleTheForkIncomingEventsUseCase } from ':modules/webhooks/platforms/thefork/use-cases/handle-thefork-incoming-events.use-case';
import {
    TheForkEntityType,
    TheForkEventRequestBody,
    TheForkEventRequestQuery,
    TheForkEventType,
} from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';

// Mock the logger module
jest.mock(':helpers/logger', () => ({
    logger: {
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    },
}));

describe('HandleTheForkIncomingEventsUseCase', () => {
    let handleTheForkUpsertedCustomerUseCaseMock: jest.Mocked<HandleTheForkUpsertedCustomerUseCase>;
    let handleTheForkUpsertedReservationUseCaseMock: jest.Mocked<HandleTheForkUpsertedReservationUseCase>;
    let loggerMock: jest.Mocked<typeof import(':helpers/logger').logger>;

    beforeEach(() => {
        container.clearInstances();

        // Mock HandleTheForkUpsertedCustomerUseCase
        handleTheForkUpsertedCustomerUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<HandleTheForkUpsertedCustomerUseCase>;

        // Mock HandleTheForkUpsertedReservationUseCase
        handleTheForkUpsertedReservationUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<HandleTheForkUpsertedReservationUseCase>;

        // Get the mocked logger
        loggerMock = jest.mocked(logger);

        // Clear all mock calls
        jest.clearAllMocks();

        container.register(HandleTheForkUpsertedCustomerUseCase, { useValue: handleTheForkUpsertedCustomerUseCaseMock });
        container.register(HandleTheForkUpsertedReservationUseCase, { useValue: handleTheForkUpsertedReservationUseCaseMock });
    });

    const createMockEventBody = (overrides: Partial<TheForkEventRequestBody> = {}): TheForkEventRequestBody => ({
        entityType: TheForkEntityType.CUSTOMER,
        eventType: TheForkEventType.CUSTOMER_CREATED,
        uuid: 'test-uuid-123',
        ...overrides,
    });

    const createMockEventQuery = (overrides: Partial<TheForkEventRequestQuery> = {}): TheForkEventRequestQuery => ({
        token: Config.platforms.lafourchette.clientApi.token as any,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully handle CUSTOMER_CREATED event', async () => {
            const mockBody = createMockEventBody({
                entityType: TheForkEntityType.CUSTOMER,
                eventType: TheForkEventType.CUSTOMER_CREATED,
            });
            const mockQuery = createMockEventQuery();

            handleTheForkUpsertedCustomerUseCaseMock.execute.mockResolvedValue();

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(handleTheForkUpsertedCustomerUseCaseMock.execute).toHaveBeenCalledWith({ body: mockBody });
            expect(loggerMock.info).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Event handled', {
                event: TheForkEventType.CUSTOMER_CREATED,
                body: mockBody,
            });
        });

        it('should successfully handle CUSTOMER_UPDATED event', async () => {
            const mockBody = createMockEventBody({
                entityType: TheForkEntityType.CUSTOMER,
                eventType: TheForkEventType.CUSTOMER_UPDATED,
            });
            const mockQuery = createMockEventQuery();

            handleTheForkUpsertedCustomerUseCaseMock.execute.mockResolvedValue();

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(handleTheForkUpsertedCustomerUseCaseMock.execute).toHaveBeenCalledWith({ body: mockBody });
            expect(loggerMock.info).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Event handled', {
                event: TheForkEventType.CUSTOMER_UPDATED,
                body: mockBody,
            });
        });

        it('should successfully handle RESERVATION_CREATED event', async () => {
            const mockBody = createMockEventBody({
                entityType: TheForkEntityType.RESERVATION,
                eventType: TheForkEventType.RESERVATION_CREATED,
            });
            const mockQuery = createMockEventQuery();

            handleTheForkUpsertedReservationUseCaseMock.execute.mockResolvedValue();

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(handleTheForkUpsertedReservationUseCaseMock.execute).toHaveBeenCalledWith({ body: mockBody });
            expect(loggerMock.info).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Event handled', {
                event: TheForkEventType.RESERVATION_CREATED,
                body: mockBody,
            });
        });

        it('should successfully handle RESERVATION_UPDATED event', async () => {
            const mockBody = createMockEventBody({
                entityType: TheForkEntityType.RESERVATION,
                eventType: TheForkEventType.RESERVATION_UPDATED,
            });
            const mockQuery = createMockEventQuery();

            handleTheForkUpsertedReservationUseCaseMock.execute.mockResolvedValue();

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(handleTheForkUpsertedReservationUseCaseMock.execute).toHaveBeenCalledWith({ body: mockBody });
            expect(loggerMock.info).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Event handled', {
                event: TheForkEventType.RESERVATION_UPDATED,
                body: mockBody,
            });
        });

        it('should return early and log error when body is missing', async () => {
            const mockQuery = createMockEventQuery();

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: null as any, query: mockQuery });

            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Body missing');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Invalid body or query', {
                body: null,
            });
            expect(handleTheForkUpsertedCustomerUseCaseMock.execute).not.toHaveBeenCalled();
            expect(handleTheForkUpsertedReservationUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should return early and log error when body format is invalid', async () => {
            const mockBody = createMockEventBody({ eventType: 'wrong-event-type' as any });
            const mockQuery = createMockEventQuery();

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Invalid body format');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Invalid body or query', {
                body: mockBody,
            });
            expect(handleTheForkUpsertedCustomerUseCaseMock.execute).not.toHaveBeenCalled();
            expect(handleTheForkUpsertedReservationUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should return early and log error when token is invalid', async () => {
            const mockBody = createMockEventBody();
            const mockQuery = createMockEventQuery({ token: 'invalid-token' } as any);

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Invalid token');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Invalid body or query', {
                body: mockBody,
            });
            expect(handleTheForkUpsertedCustomerUseCaseMock.execute).not.toHaveBeenCalled();
            expect(handleTheForkUpsertedReservationUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should handle and log errors when customer use case throws an error', async () => {
            const mockBody = createMockEventBody({
                entityType: TheForkEntityType.CUSTOMER,
                eventType: TheForkEventType.CUSTOMER_CREATED,
            });
            const mockQuery = createMockEventQuery();
            const mockError = new Error('Customer use case error');

            handleTheForkUpsertedCustomerUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(handleTheForkUpsertedCustomerUseCaseMock.execute).toHaveBeenCalledWith({ body: mockBody });
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Error handling incoming event', {
                body: mockBody,
                err: mockError,
            });
            expect(loggerMock.info).not.toHaveBeenCalled();
        });

        it('should handle and log errors when reservation use case throws an error', async () => {
            const mockBody = createMockEventBody({
                entityType: TheForkEntityType.RESERVATION,
                eventType: TheForkEventType.RESERVATION_CREATED,
            });
            const mockQuery = createMockEventQuery();
            const mockError = new Error('Reservation use case error');

            handleTheForkUpsertedReservationUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(HandleTheForkIncomingEventsUseCase);
            await useCase.execute({ body: mockBody, query: mockQuery });

            expect(handleTheForkUpsertedReservationUseCaseMock.execute).toHaveBeenCalledWith({ body: mockBody });
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Error handling incoming event', {
                body: mockBody,
                err: mockError,
            });
            expect(loggerMock.info).not.toHaveBeenCalled();
        });
    });
});
