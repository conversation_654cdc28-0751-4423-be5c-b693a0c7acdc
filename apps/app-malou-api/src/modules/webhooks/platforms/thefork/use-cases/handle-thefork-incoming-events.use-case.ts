import 'reflect-metadata';

import ':env';

import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { HandleTheForkUpsertedCustomerUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-customer/handle-thefork-upserted-customer.use-case';
import { HandleTheForkUpsertedReservationUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-reservation/handle-thefork-upserted-reservation.use-case';
import {
    TheForkEventRequestBody,
    theForkEventRequestBodyValidator,
    TheForkEventRequestQuery,
    TheForkEventType,
} from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';

@singleton()
export class HandleTheForkIncomingEventsUseCase {
    constructor(
        private readonly _handleTheForkUpsertedCustomerUseCase: HandleTheForkUpsertedCustomerUseCase,
        private readonly _handleTheForkUpsertedReservationUseCase: HandleTheForkUpsertedReservationUseCase
    ) {}

    async execute({ body, query }: { body: TheForkEventRequestBody; query: TheForkEventRequestQuery }): Promise<void> {
        try {
            // Check body and query
            const isBodyAndQueryValid = await this._checkEventBodyAndQuery({ body, query });
            if (!isBodyAndQueryValid) {
                logger.error('[WEBHOOKS] [THEFORK] - Invalid body or query', {
                    body,
                });
                return;
            }

            // Handle incoming events
            const theForkEventHandler = {
                [TheForkEventType.CUSTOMER_CREATED]: this._handleTheForkUpsertedCustomerUseCase,
                [TheForkEventType.CUSTOMER_UPDATED]: this._handleTheForkUpsertedCustomerUseCase,
                [TheForkEventType.RESERVATION_CREATED]: this._handleTheForkUpsertedReservationUseCase,
                [TheForkEventType.RESERVATION_UPDATED]: this._handleTheForkUpsertedReservationUseCase,
            }[body.eventType];

            if (!theForkEventHandler) {
                logger.error('[WEBHOOKS] [THEFORK] - Event not supported', {
                    event: body.eventType,
                    body,
                });
                return;
            }

            await theForkEventHandler.execute({
                body,
            });
            logger.info('[WEBHOOKS] [THEFORK] - Event handled', {
                event: body.eventType,
                body,
            });
        } catch (err) {
            logger.error('[WEBHOOKS] [THEFORK] - Error handling incoming event', {
                body,
                err,
            });
        }
    }

    private async _checkEventBodyAndQuery({
        body,
        query,
    }: {
        body: TheForkEventRequestBody;
        query: TheForkEventRequestQuery;
    }): Promise<boolean> {
        if (!body) {
            logger.error('[WEBHOOKS] [THEFORK] - Body missing');
            return false;
        }

        const { success } = await theForkEventRequestBodyValidator.safeParseAsync(body);
        if (!success) {
            logger.error('[WEBHOOKS] [THEFORK] - Invalid body format');
            return false;
        }

        if (query.token !== Config.platforms.lafourchette.clientApi.token) {
            logger.error('[WEBHOOKS] [THEFORK] - Invalid token');
            return false;
        }

        return true;
    }
}
