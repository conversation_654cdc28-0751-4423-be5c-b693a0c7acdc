import { Builder } from 'builder-pattern';

import { IProviderClient, newDbId } from '@malou-io/package-models';
import { ProviderClientSource } from '@malou-io/package-utils';

type ProviderClientPayload = IProviderClient;

const _buildProviderClient = (providerClient: ProviderClientPayload) => Builder<ProviderClientPayload>(providerClient);

export const getDefaultProviderClient = () =>
    _buildProviderClient({
        _id: newDbId(),
        providerClientId: 'test-provider-client-' + newDbId().toString(),
        restaurantId: newDbId(),
        source: ProviderClientSource.LAFOURCHETTE,
        firstName: '<PERSON>',
        lastName: 'Doe',
        birthday: new Date('1990-01-01'),
        address: undefined,
        email: '<EMAIL>',
        phone: {
            prefix: 33,
            digits: 123456789,
        },
        language: undefined,
        civility: undefined,
        contactOptions: [],
        visits: [],
    });
