import { chunk } from 'lodash';
import { singleton } from 'tsyringe';

import { isNotNil, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { TheForkApiProvider } from ':modules/clients/provider-clients/providers/thefork/thefork-api-provider';
import { TheForkClientApiProviderErrorObject } from ':modules/clients/provider-clients/providers/thefork/thefork-client-api-provider.definitions';
import {
    TheForkClient,
    TheForkClientPagination,
    TheForkReservation,
    TheForkReservationPagination,
} from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class TheForkService {
    constructor(
        private readonly _theForkProvider: TheForkApiProvider,
        private readonly _slackService: SlackService
    ) {}

    async fetchClient(providerClientId: string): Promise<TheForkClient | null> {
        const result = await this._theForkProvider.getClientById(providerClientId);
        if (result.isErr()) {
            this._handleError(result.error, 'fetchClient');
            return null;
        }
        return result.value;
    }

    async fetchClientList(startDate: Date, endDate: Date, restaurantId: string): Promise<TheForkClient[]> {
        const groupUuid = this.mapRestaurantIdToTheForkGroupUuid(restaurantId);
        // TheFork clients / customers are grouped by groupUuid not by restaurantUuid
        const clientIds: string[] = [];
        let result: TheForkClientPagination = { totalCount: 0, limit: 0, page: 0, data: [] };
        const limit = 100;
        let page = 1;
        do {
            const theForkResult = await this._theForkProvider.getClientIds({ startDate, endDate, groupUuid, limit, page });
            if (theForkResult.isErr()) {
                this._handleError(theForkResult.error, 'fetchClientList', restaurantId);
                break;
            }
            result = theForkResult.value;
            page++;
            clientIds.push(...result.data);
            // eslint-disable-next-line no-mixed-operators
        } while ((result.page - 1) * result.limit + result.data.length < result.totalCount);

        const CHUNK_SIZE = 10;
        const chunks = chunk(clientIds, CHUNK_SIZE);
        const ONE_SECOND = 1 * TimeInMilliseconds.SECOND;

        const clients: TheForkClient[] = [];

        for (const clientIdsChunk of chunks) {
            await waitFor(ONE_SECOND);
            const newClients = await Promise.all(clientIdsChunk.map((clientId) => this.fetchClient(clientId)));
            clients.push(...newClients.filter(isNotNil));
        }

        return clients;
    }

    async fetchReservation(reservationId: string): Promise<TheForkReservation | null> {
        const result = await this._theForkProvider.getReservation(reservationId);
        if (result.isErr()) {
            this._handleError(result.error, 'fetchReservation');
            return null;
        }
        return result.value;
    }

    async fetchReservationList(startDate: Date, endDate: Date, restaurantId: string): Promise<TheForkReservation[]> {
        const restaurantUuid = this.mapRestaurantIdToTheForkRestaurantUuid(restaurantId);

        const limit = 100;
        let page = 1;
        const reservationIds: string[] = [];
        let result: TheForkReservationPagination = { totalCount: 0, limit: 0, page: 0, data: [] };
        do {
            const theForkResult = await this._theForkProvider.getReservationIds({
                startDate,
                endDate,
                limit,
                page,
                restaurantUuid,
                groupUuid: undefined,
            });
            if (theForkResult.isErr()) {
                this._handleError(theForkResult.error, 'fetchReservationList', restaurantId);
                break;
            }
            result = theForkResult.value;
            reservationIds.push(...result.data);
            page++;
            // eslint-disable-next-line no-mixed-operators
        } while ((result.page - 1) * result.limit + result.data.length < result.totalCount);

        const ONE_SECOND = 1 * TimeInMilliseconds.SECOND;
        const CHUNK_SIZE = 10;
        const chunks = chunk(reservationIds, CHUNK_SIZE);

        const reservations: TheForkReservation[] = [];

        for (const clientIdsChunk of chunks) {
            await waitFor(ONE_SECOND);
            const newReservations = await Promise.all(clientIdsChunk.map((reservationId) => this.fetchReservation(reservationId)));
            reservations.push(...newReservations.filter(isNotNil));
        }

        return reservations;
    }

    mapRestaurantIdToTheForkGroupUuid(_restaurantId: string): string {
        return '8b64e390-a832-4155-bf99-f61899a6825e'; // TODO get the groupUuid from the restaurant
    }

    mapRestaurantIdToTheForkRestaurantUuid(_restaurantId: string): string {
        return 'a83a426c-c8d2-4223-a16b-19b40f3c46d3'; // TODO get the restaurantUuid from the restaurant
    }

    mapTheForkRestaurantUuidToRestaurantIds(_restaurantUuid: string): string[] {
        return ['65719e39827a77655ea28f2d']; // TODO get the restaurantIds from the restaurantUuid
    }

    private _handleError(error: TheForkClientApiProviderErrorObject, method: string, restaurantId?: string): void {
        logger.error('[TheForkService] Error', { error, restaurantId, method });
        const line1 = `THEFORK restaurantId: ${restaurantId}`;
        const line2 = `ErrorCode: ${error.code}  Method: ${method}`;
        const line3 = `\`\`\`${error.stringifiedRawError}\`\`\``;
        const text = `${line1}\n${line2}\n${line3}`;
        this._slackService.sendMessage({ text, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
    }
}
