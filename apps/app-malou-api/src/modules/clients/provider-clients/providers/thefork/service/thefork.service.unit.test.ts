import { err, ok } from 'neverthrow';
import { container } from 'tsyringe';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkApiProvider } from ':modules/clients/provider-clients/providers/thefork/thefork-api-provider';
import {
    TheForkClientApiProviderErrorCode,
    TheForkClientApiProviderErrorObject,
} from ':modules/clients/provider-clients/providers/thefork/thefork-client-api-provider.definitions';
import {
    TheForkCivility,
    TheForkClient,
    TheForkClientPagination,
    TheForkReservation,
    TheForkReservationPagination,
} from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { SlackChannel, SlackService } from ':services/slack.service';

// Mock waitFor to avoid delays in tests
jest.mock('@malou-io/package-utils', () => ({
    ...jest.requireActual('@malou-io/package-utils'),
    waitFor: jest.fn().mockResolvedValue(undefined),
}));

describe('TheForkService', () => {
    let theForkApiProviderMock: jest.Mocked<TheForkApiProvider>;
    let slackServiceMock: jest.Mocked<SlackService>;

    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['RestaurantsRepository']);

        // Mock TheForkApiProvider
        theForkApiProviderMock = {
            getClientById: jest.fn(),
            getClientIds: jest.fn(),
            getReservation: jest.fn(),
            getReservationIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkApiProvider>;

        // Mock SlackService
        slackServiceMock = {
            sendMessage: jest.fn(),
            sendAlert: jest.fn(),
            createContextForSlack: jest.fn(),
        } as unknown as jest.Mocked<SlackService>;

        container.register(TheForkApiProvider, { useValue: theForkApiProviderMock });
        container.register(SlackService, { useValue: slackServiceMock });
    });

    const createMockClient = (overrides: Partial<TheForkClient> = {}): TheForkClient => ({
        customerUuid: 'test-customer-uuid',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        birthDate: '1990-01-01',
        locale: 'fr_FR',
        civility: TheForkCivility.MR,
        rank: 'gold',
        computedRank: 'gold',
        isVip: false,
        address: '123 Test Street',
        allergiesAndIntolerances: null,
        dietaryRestrictions: null,
        favFood: null,
        favDrinks: null,
        favSeating: null,
        notes: null,
        originRestaurantUuid: null,
        originRestaurantName: null,
        creationDate: '2023-01-01T00:00:00Z',
        lastUpdateAt: '2023-01-01T00:00:00Z',
        isPromoter: false,
        secondaryPhone: null,
        country: 'France',
        city: 'Paris',
        zipcode: '75001',
        optins: {
            restaurantNewsletter: true,
        },
        customFields: null,
        ...overrides,
    });

    const createMockReservation = (overrides: Partial<TheForkReservation> = {}): TheForkReservation => ({
        reservationUuid: 'test-reservation-uuid',
        restaurantUuid: 'test-restaurant-uuid',
        mealDate: '2023-12-25T19:00:00Z',
        partySize: 4,
        status: 'CONFIRMED' as any,
        offerUuid: null,
        customerNote: null,
        customerUuid: 'test-customer-uuid',
        customFields: null,
        offerDetails: null,
        utmTrackingInformation: null,
        billAmount: null,
        reservationChannel: 'TheFork' as any,
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        ...overrides,
    });

    const createMockError = (code: string = TheForkClientApiProviderErrorCode.UNKNOWN_ERROR): TheForkClientApiProviderErrorObject => ({
        code: code as any,
        stringifiedRawError: 'Mock error details',
    });

    describe('fetchClient', () => {
        it('should return client when API call succeeds', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockClient = createMockClient();
            theForkApiProviderMock.getClientById.mockResolvedValue(ok(mockClient));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchClient('test-client-id');

            expect(result).toEqual(mockClient);
            expect(theForkApiProviderMock.getClientById).toHaveBeenCalledWith('test-client-id');
        });

        it('should return null and handle error when API call fails', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockError = createMockError();
            theForkApiProviderMock.getClientById.mockResolvedValue(err(mockError));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchClient('test-client-id');

            expect(result).toBeNull();
            expect(theForkApiProviderMock.getClientById).toHaveBeenCalledWith('test-client-id');
            expect(slackServiceMock.sendMessage).toHaveBeenCalledWith({
                text: expect.stringContaining('THEFORK restaurantId: undefined'),
                channel: SlackChannel.POSTS_V2_ALERTS,
                shouldPing: true,
            });
        });
    });

    describe('fetchClientList', () => {
        it('should return clients when API calls succeed', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockClientPagination: TheForkClientPagination = {
                totalCount: 2,
                limit: 100,
                page: 1,
                data: ['client-1', 'client-2'],
            };

            const mockClient1 = createMockClient({ customerUuid: 'client-1' });
            const mockClient2 = createMockClient({ customerUuid: 'client-2' });

            theForkApiProviderMock.getClientIds.mockResolvedValue(ok(mockClientPagination));
            theForkApiProviderMock.getClientById.mockResolvedValueOnce(ok(mockClient1)).mockResolvedValueOnce(ok(mockClient2));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchClientList(startDate, endDate, restaurantId);

            expect(result).toEqual([mockClient1, mockClient2]);
            expect(theForkApiProviderMock.getClientIds).toHaveBeenCalledWith({
                startDate,
                endDate,
                groupUuid: '8b64e390-a832-4155-bf99-f61899a6825e',
                limit: 100,
                page: 1,
            });
            expect(theForkApiProviderMock.getClientById).toHaveBeenCalledTimes(2);
        });

        it('should handle pagination correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockClientPagination1: TheForkClientPagination = {
                totalCount: 150,
                limit: 100,
                page: 1,
                data: Array.from({ length: 100 }, (_, i) => `client-${i + 1}`),
            };

            const mockClientPagination2: TheForkClientPagination = {
                totalCount: 150,
                limit: 100,
                page: 2,
                data: Array.from({ length: 50 }, (_, i) => `client-${i + 101}`),
            };

            theForkApiProviderMock.getClientIds
                .mockResolvedValueOnce(ok(mockClientPagination1))
                .mockResolvedValueOnce(ok(mockClientPagination2));

            // Mock all client fetches to return valid clients
            for (let i = 1; i <= 150; i++) {
                theForkApiProviderMock.getClientById.mockResolvedValueOnce(ok(createMockClient({ customerUuid: `client-${i}` })));
            }

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchClientList(startDate, endDate, restaurantId);

            expect(result).toHaveLength(150);
            expect(theForkApiProviderMock.getClientIds).toHaveBeenCalledTimes(2);
            expect(theForkApiProviderMock.getClientById).toHaveBeenCalledTimes(150);
        });

        it('should handle API errors and send Slack notification', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockError = createMockError();
            theForkApiProviderMock.getClientIds.mockResolvedValue(err(mockError));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchClientList(startDate, endDate, restaurantId);

            expect(result).toEqual([]);
            expect(slackServiceMock.sendMessage).toHaveBeenCalledWith({
                text: expect.stringContaining(`THEFORK restaurantId: ${restaurantId}`),
                channel: SlackChannel.POSTS_V2_ALERTS,
                shouldPing: true,
            });
        });

        it('should filter out null clients from failed individual fetches', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockClientPagination: TheForkClientPagination = {
                totalCount: 3,
                limit: 100,
                page: 1,
                data: ['client-1', 'client-2', 'client-3'],
            };

            const mockClient1 = createMockClient({ customerUuid: 'client-1' });
            const mockClient3 = createMockClient({ customerUuid: 'client-3' });

            theForkApiProviderMock.getClientIds.mockResolvedValue(ok(mockClientPagination));
            theForkApiProviderMock.getClientById
                .mockResolvedValueOnce(ok(mockClient1))
                .mockResolvedValueOnce(err(createMockError())) // client-2 fails
                .mockResolvedValueOnce(ok(mockClient3));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchClientList(startDate, endDate, restaurantId);

            expect(result).toEqual([mockClient1, mockClient3]);
            expect(result).toHaveLength(2);
        });
    });

    describe('fetchReservation', () => {
        it('should return reservation when API call succeeds', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockReservation = createMockReservation();
            theForkApiProviderMock.getReservation.mockResolvedValue(ok(mockReservation));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchReservation('test-reservation-id');

            expect(result).toEqual(mockReservation);
            expect(theForkApiProviderMock.getReservation).toHaveBeenCalledWith('test-reservation-id');
        });

        it('should return null and handle error when API call fails', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockError = createMockError();
            theForkApiProviderMock.getReservation.mockResolvedValue(err(mockError));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchReservation('test-reservation-id');

            expect(result).toBeNull();
            expect(theForkApiProviderMock.getReservation).toHaveBeenCalledWith('test-reservation-id');
            expect(slackServiceMock.sendMessage).toHaveBeenCalledWith({
                text: expect.stringContaining('THEFORK restaurantId: undefined'),
                channel: SlackChannel.POSTS_V2_ALERTS,
                shouldPing: true,
            });
        });
    });

    describe('fetchReservationList', () => {
        it('should return reservations when API calls succeed', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockReservationPagination: TheForkReservationPagination = {
                totalCount: 2,
                limit: 100,
                page: 1,
                data: ['reservation-1', 'reservation-2'],
            };

            const mockReservation1 = createMockReservation({ reservationUuid: 'reservation-1' });
            const mockReservation2 = createMockReservation({ reservationUuid: 'reservation-2' });

            theForkApiProviderMock.getReservationIds.mockResolvedValue(ok(mockReservationPagination));
            theForkApiProviderMock.getReservation.mockResolvedValueOnce(ok(mockReservation1)).mockResolvedValueOnce(ok(mockReservation2));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchReservationList(startDate, endDate, restaurantId);

            expect(result).toEqual([mockReservation1, mockReservation2]);
            expect(theForkApiProviderMock.getReservationIds).toHaveBeenCalledWith({
                startDate,
                endDate,
                limit: 100,
                page: 1,
                restaurantUuid: 'a83a426c-c8d2-4223-a16b-19b40f3c46d3',
                groupUuid: undefined,
            });
            expect(theForkApiProviderMock.getReservation).toHaveBeenCalledTimes(2);
        });

        it('should handle pagination correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockReservationPagination1: TheForkReservationPagination = {
                totalCount: 150,
                limit: 100,
                page: 1,
                data: Array.from({ length: 100 }, (_, i) => `reservation-${i + 1}`),
            };

            const mockReservationPagination2: TheForkReservationPagination = {
                totalCount: 150,
                limit: 100,
                page: 2,
                data: Array.from({ length: 50 }, (_, i) => `reservation-${i + 101}`),
            };

            theForkApiProviderMock.getReservationIds
                .mockResolvedValueOnce(ok(mockReservationPagination1))
                .mockResolvedValueOnce(ok(mockReservationPagination2));

            // Mock all reservation fetches to return valid reservations
            for (let i = 1; i <= 150; i++) {
                theForkApiProviderMock.getReservation.mockResolvedValueOnce(
                    ok(createMockReservation({ reservationUuid: `reservation-${i}` }))
                );
            }

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchReservationList(startDate, endDate, restaurantId);

            expect(result).toHaveLength(150);
            expect(theForkApiProviderMock.getReservationIds).toHaveBeenCalledTimes(2);
            expect(theForkApiProviderMock.getReservation).toHaveBeenCalledTimes(150);
        });

        it('should handle API errors and send Slack notification', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockError = createMockError();
            theForkApiProviderMock.getReservationIds.mockResolvedValue(err(mockError));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchReservationList(startDate, endDate, restaurantId);

            expect(result).toEqual([]);
            expect(slackServiceMock.sendMessage).toHaveBeenCalledWith({
                text: expect.stringContaining(`THEFORK restaurantId: ${restaurantId}`),
                channel: SlackChannel.POSTS_V2_ALERTS,
                shouldPing: true,
            });
        });

        it('should filter out null reservations from failed individual fetches', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockReservationPagination: TheForkReservationPagination = {
                totalCount: 3,
                limit: 100,
                page: 1,
                data: ['reservation-1', 'reservation-2', 'reservation-3'],
            };

            const mockReservation1 = createMockReservation({ reservationUuid: 'reservation-1' });
            const mockReservation3 = createMockReservation({ reservationUuid: 'reservation-3' });

            theForkApiProviderMock.getReservationIds.mockResolvedValue(ok(mockReservationPagination));
            theForkApiProviderMock.getReservation
                .mockResolvedValueOnce(ok(mockReservation1))
                .mockResolvedValueOnce(err(createMockError())) // reservation-2 fails
                .mockResolvedValueOnce(ok(mockReservation3));

            const theForkService = container.resolve(TheForkService);
            const result = await theForkService.fetchReservationList(startDate, endDate, restaurantId);

            expect(result).toEqual([mockReservation1, mockReservation3]);
            expect(result).toHaveLength(2);
        });
    });

    describe('mapRestaurantIdToTheForkGroupUuid', () => {
        it('should return hardcoded group UUID', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const theForkService = container.resolve(TheForkService);
            const result = theForkService.mapRestaurantIdToTheForkGroupUuid('any-restaurant-id');

            expect(result).toBe('8b64e390-a832-4155-bf99-f61899a6825e');
        });
    });

    describe('mapRestaurantIdToTheForkRestaurantUuid', () => {
        it('should return hardcoded restaurant UUID', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const theForkService = container.resolve(TheForkService);
            const result = theForkService.mapRestaurantIdToTheForkRestaurantUuid('any-restaurant-id');

            expect(result).toBe('a83a426c-c8d2-4223-a16b-19b40f3c46d3');
        });
    });

    describe('mapTheForkRestaurantUuidToRestaurantIds', () => {
        it('should return hardcoded restaurant IDs array', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const theForkService = container.resolve(TheForkService);
            const result = theForkService.mapTheForkRestaurantUuidToRestaurantIds('any-restaurant-uuid');

            expect(result).toEqual(['65719e39827a77655ea28f2d']);
        });
    });

    describe('_handleError (private method behavior)', () => {
        it('should send Slack message with correct format when error occurs', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockError = createMockError(TheForkClientApiProviderErrorCode.CUSTOMER_NOT_FOUND);
            theForkApiProviderMock.getClientById.mockResolvedValue(err(mockError));

            const theForkService = container.resolve(TheForkService);
            await theForkService.fetchClient('test-client-id');

            expect(slackServiceMock.sendMessage).toHaveBeenCalledWith({
                text: expect.stringMatching(
                    new RegExp(
                        'THEFORK restaurantId: undefined' +
                            '\n' +
                            'ErrorCode: CUSTOMER_NOT_FOUND  Method: fetchClient' +
                            '\n' +
                            '```Mock error details```'
                    )
                ),
                channel: SlackChannel.POSTS_V2_ALERTS,
                shouldPing: true,
            });
        });

        it('should include restaurant ID in Slack message when provided', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const restaurantId = 'test-restaurant-123';
            const mockError = createMockError(TheForkClientApiProviderErrorCode.UNAUTHORIZED);
            theForkApiProviderMock.getClientIds.mockResolvedValue(err(mockError));

            const theForkService = container.resolve(TheForkService);
            await theForkService.fetchClientList(new Date(), new Date(), restaurantId);

            expect(slackServiceMock.sendMessage).toHaveBeenCalledWith({
                text: expect.stringMatching(
                    new RegExp(
                        `THEFORK restaurantId: ${restaurantId}` +
                            '\n' +
                            'ErrorCode: UNAUTHORIZED  Method: fetchClientList' +
                            '\n' +
                            '```Mock error details```'
                    )
                ),
                channel: SlackChannel.POSTS_V2_ALERTS,
                shouldPing: true,
            });
        });
    });
});
