import axios, { AxiosResponse } from 'axios';
import { DateTime } from 'luxon';
import { Err, err, ok, Result } from 'neverthrow';
import { singleton } from 'tsyringe';
import { ZodType } from 'zod';

import { errorReplacer } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import {
    TheForkClientApiErrorCode,
    theForkClientApiErrorResponseValidator,
    TheForkClientApiErrorSubCode,
    TheForkClientApiProviderErrorCode,
    TheForkClientApiProviderErrorObject,
    TheForkClientApiRequestOptions,
    theForkOAuthResponseValidator,
} from ':modules/clients/provider-clients/providers/thefork/thefork-client-api-provider.definitions';
import {
    TheForkClient,
    TheForkClientPagination,
    theForkClientPaginationValidator,
    theForkClientValidator,
    TheForkReservation,
    TheForkReservationPagination,
    theForkReservationPaginationValidator,
    theForkReservationValidator,
} from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';

@singleton()
export class TheForkApiProvider {
    async getClientById(customerUuid: string): Promise<Result<TheForkClient, TheForkClientApiProviderErrorObject>> {
        const token = await this._getToken();

        return this._callApi({
            responseValidator: theForkClientValidator,
            token,
            requestOptions: { method: 'GET', endpoint: `/manager/v1/customers/${customerUuid}`, queryParams: {} },
        });
    }

    async getClientIds({
        startDate,
        endDate,
        groupUuid,
        limit,
        page,
    }: {
        startDate: Date;
        endDate: Date;
        groupUuid: string;
        limit: number;
        page: number;
    }): Promise<Result<TheForkClientPagination, TheForkClientApiProviderErrorObject>> {
        const token = await this._getToken();

        const formattedStartDate = DateTime.fromJSDate(startDate).toISODate();
        const formattedEndDate = DateTime.fromJSDate(endDate).toISODate();
        const params = { startDate: formattedStartDate, endDate: formattedEndDate, groupUuid, limit, page };

        return this._callApi({
            responseValidator: theForkClientPaginationValidator,
            token,
            requestOptions: { method: 'GET', endpoint: '/manager/v1/customers', queryParams: params },
        });
    }

    async getReservation(reservationId: string): Promise<Result<TheForkReservation, TheForkClientApiProviderErrorObject>> {
        const token = await this._getToken();

        return this._callApi({
            responseValidator: theForkReservationValidator,
            token,
            requestOptions: { method: 'GET', endpoint: `/manager/v1/reservations/${reservationId}`, queryParams: {} },
        });
    }

    async getReservationIds({
        startDate,
        endDate,
        limit,
        page,
        restaurantUuid,
        groupUuid,
    }: {
        startDate: Date;
        endDate: Date;
        limit: number;
        page: number;
        restaurantUuid?: string;
        groupUuid?: string;
    }): Promise<Result<TheForkReservationPagination, TheForkClientApiProviderErrorObject>> {
        const token = await this._getToken();

        const formattedStartDate = DateTime.fromJSDate(startDate).toISODate();
        const formattedEndDate = DateTime.fromJSDate(endDate).toISODate();
        const params: Record<string, string | number> = { startDate: formattedStartDate, endDate: formattedEndDate, limit, page };
        if (restaurantUuid) {
            params.restaurantUuid = restaurantUuid;
        }
        if (groupUuid) {
            params.groupUuid = groupUuid;
        }

        return this._callApi({
            responseValidator: theForkReservationPaginationValidator,
            token,
            requestOptions: { method: 'GET', endpoint: '/manager/v1/reservations', queryParams: params },
        });
    }

    private async _getToken(hostname?: string): Promise<string> {
        const url = 'https://auth.thefork.io/oauth/token';
        const body = {
            audience: 'https://' + (hostname ?? 'api.thefork.io'),
            client_id: Config.platforms.lafourchette.clientApi.clientId ?? '',
            client_secret: Config.platforms.lafourchette.clientApi.clientSecret ?? '',
            grant_type: 'client_credentials',
        };

        const result = await axios.post(url, body);

        const validatedResult = theForkOAuthResponseValidator.safeParse(result.data);
        if (!validatedResult.success) {
            logger.error('[TheForkApiProvider._getToken] Error CANNOT_VALIDATE_RESPONSE', {
                error: validatedResult.error,
                response: result.data,
            });
            throw new Error('CANNOT_VALIDATE_RESPONSE');
        }

        return validatedResult.data.access_token;
    }

    private async _callApi<T>(params: {
        responseValidator: ZodType<T>;
        token: string;
        requestOptions: TheForkClientApiRequestOptions;
    }): Promise<Result<T, TheForkClientApiProviderErrorObject>> {
        logger.info('[TheForkApiProvider.callApi] Start', {
            requestOptions: params.requestOptions,
            responseValidatorDescription: params.responseValidator.description,
        });
        let res: AxiosResponse;
        try {
            res = await axios({
                method: params.requestOptions.method,
                baseURL: 'https://' + (params.requestOptions.hostname ?? 'api.thefork.io'),
                url: params.requestOptions.endpoint,
                params: { ...params.requestOptions.queryParams },
                headers: params.requestOptions.headers
                    ? { ...params.requestOptions.headers, Authorization: `Bearer ${params.token}` }
                    : { Authorization: `Bearer ${params.token}` },
            });
        } catch (error: unknown) {
            logger.error('[TheForkApiProvider.callApi] Error', { error });
            return this._handleError(error);
        }

        logger.info('[TheForkApiProvider.callApi] OK', { res: this._stringifyResultWithLimit(res.data) });
        const validatedResData = params.responseValidator.safeParse(res.data);
        if (!validatedResData.success) {
            logger.error('[TheForkApiProvider.callApi] Error CANNOT_VALIDATE_RESPONSE', {
                error: validatedResData.error,
                response: res.data,
            });
            return err({
                code: TheForkClientApiProviderErrorCode.CANNOT_VALIDATE_RESPONSE,
                stringifiedRawError: JSON.stringify(validatedResData.error), // Do not use errorReplacer for ZodError
            });
        }
        return ok(validatedResData.data);
    }

    private _handleError(error: unknown): Err<never, TheForkClientApiProviderErrorObject> {
        let stringifiedRawError = JSON.stringify(error, errorReplacer);
        if (axios.isAxiosError(error)) {
            stringifiedRawError = JSON.stringify(
                { data: error.response?.data, status: error.response?.status, statusText: error.response?.statusText },
                errorReplacer
            );
            const errorResponse = theForkClientApiErrorResponseValidator.safeParse(error.response?.data);
            if (!errorResponse.success) {
                logger.error('[TheForkApiProvider.callApi] Error CANNOT_VALIDATE_ERROR_RESPONSE', {
                    stringifiedRawError,
                    error: errorResponse.error,
                });
                return err({
                    code: TheForkClientApiProviderErrorCode.CANNOT_VALIDATE_ERROR_RESPONSE,
                    stringifiedRawError: JSON.stringify(errorResponse.error), // Do not use errorReplacer for ZodError,
                });
            }
            const code = errorResponse.data.error;
            const subCode = errorResponse.data.data.code;
            if (code === TheForkClientApiErrorCode.UNAUTHORIZED) {
                logger.error('[TheForkApiProvider.callApi] Error UNAUTHORIZED');
                return err({ code: TheForkClientApiProviderErrorCode.UNAUTHORIZED, stringifiedRawError });
            }
            if (code === TheForkClientApiErrorCode.NOT_FOUND && subCode === TheForkClientApiErrorSubCode.RESERVATION_NOT_FOUND) {
                logger.error('[TheForkApiProvider.callApi] Error RESERVATION_NOT_FOUND');
                return err({
                    code: TheForkClientApiProviderErrorCode.RESERVATION_NOT_FOUND,
                    stringifiedRawError,
                });
            }
            if (code === TheForkClientApiErrorCode.NOT_FOUND && subCode === TheForkClientApiErrorSubCode.CUSTOMER_NOT_FOUND) {
                logger.error('[TheForkApiProvider.callApi] Error CUSTOMER_NOT_FOUND');
                return err({
                    code: TheForkClientApiProviderErrorCode.CUSTOMER_NOT_FOUND,
                    stringifiedRawError,
                });
            }
        }
        logger.error('[TheForkApiProvider.callApi] Error UNKNOWN_ERROR');
        return err({ code: TheForkClientApiProviderErrorCode.UNKNOWN_ERROR, stringifiedRawError });
    }

    private _stringifyResultWithLimit(res: any): string {
        try {
            return JSON.stringify(res).substring(0, 250);
        } catch (error) {
            logger.warn('_stringifyResultWithLimit', error);
            return 'error stringify result';
        }
    }
}
