import { container } from 'tsyringe';

import { ProviderClientSource } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkCivility, TheForkClient } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { HandleTheForkUpsertedCustomerUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-customer/handle-thefork-upserted-customer.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import {
    TheForkEntityType,
    TheForkEventRequestBody,
    TheForkEventType,
} from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';

// Mock the logger module
jest.mock(':helpers/logger', () => ({
    logger: {
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    },
}));

describe('HandleTheForkUpsertedCustomerUseCase', () => {
    let theForkServiceMock: jest.Mocked<TheForkService>;
    let loggerMock: jest.Mocked<typeof import(':helpers/logger').logger>;

    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['RestaurantsRepository', 'ProviderClientsRepository']);

        // Mock TheForkService
        theForkServiceMock = {
            fetchClient: jest.fn(),
            fetchClientList: jest.fn(),
            fetchReservation: jest.fn(),
            fetchReservationList: jest.fn(),
            mapRestaurantIdToTheForkGroupUuid: jest.fn(),
            mapRestaurantIdToTheForkRestaurantUuid: jest.fn(),
            mapTheForkRestaurantUuidToRestaurantIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkService>;

        // Get the mocked logger
        loggerMock = jest.mocked(logger);

        // Clear all mock calls
        jest.clearAllMocks();

        container.register(TheForkService, { useValue: theForkServiceMock });
    });

    const createMockTheForkClient = (overrides: Partial<TheForkClient> = {}): TheForkClient => ({
        customerUuid: 'test-customer-uuid',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        birthDate: '1990-01-01',
        locale: 'fr_FR',
        civility: TheForkCivility.MR,
        rank: 'gold',
        computedRank: 'gold',
        isVip: false,
        address: '123 Test Street',
        allergiesAndIntolerances: null,
        dietaryRestrictions: null,
        favFood: null,
        favDrinks: null,
        favSeating: null,
        notes: null,
        originRestaurantUuid: 'origin-restaurant-uuid',
        originRestaurantName: 'Origin Restaurant',
        creationDate: '2023-01-01T00:00:00Z',
        lastUpdateAt: '2023-01-01T00:00:00Z',
        isPromoter: false,
        secondaryPhone: null,
        country: 'France',
        city: 'Paris',
        zipcode: '75001',
        optins: {
            restaurantNewsletter: true,
        },
        customFields: null,
        ...overrides,
    });

    const createMockEventBody = (overrides: Partial<TheForkEventRequestBody> = {}): TheForkEventRequestBody => ({
        entityType: TheForkEntityType.CUSTOMER,
        eventType: TheForkEventType.CUSTOMER_CREATED,
        uuid: 'test-customer-uuid',
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully upsert provider clients when TheFork client is found with origin restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();

            const restaurantId1 = testCase.getSeededObjects().restaurants[0]._id.toString();
            const restaurantId2 = testCase.getSeededObjects().restaurants[1]._id.toString();
            const restaurantIds = [restaurantId1, restaurantId2];

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Verify no provider clients exist before the operation
            const clientsBeforeOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsBeforeOperation).toHaveLength(0);

            await useCase.execute({ body: mockEventBody });

            // Verify provider clients were created in the database
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(2);

            // Verify the clients have the correct data
            const client1 = clientsAfterOperation.find((c) => c.restaurantId === restaurantId1);
            const client2 = clientsAfterOperation.find((c) => c.restaurantId === restaurantId2);

            expect(client1).toBeDefined();
            expect(client1?.providerClientId).toBe('test-customer-uuid');
            expect(client1?.source).toBe(ProviderClientSource.LAFOURCHETTE);
            expect(client1?.firstName).toBe('John');
            expect(client1?.lastName).toBe('Doe');
            expect(client1?.email).toBe('<EMAIL>');

            expect(client2).toBeDefined();
            expect(client2?.providerClientId).toBe('test-customer-uuid');
            expect(client2?.source).toBe(ProviderClientSource.LAFOURCHETTE);
            expect(client2?.firstName).toBe('John');
            expect(client2?.lastName).toBe('Doe');
            expect(client2?.email).toBe('<EMAIL>');

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
        });

        it('should return early and log error when TheFork client is not found', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            theForkServiceMock.fetchClient.mockResolvedValue(null);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Customer not found', {
                customerUuid: 'test-customer-uuid',
            });
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).not.toHaveBeenCalled();

            // Verify no provider clients were created in the database
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should return early and log error when TheFork client has no origin restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient({ originRestaurantUuid: null });
            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Customer has no origin restaurant', {
                customerUuid: 'test-customer-uuid',
            });
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).not.toHaveBeenCalled();

            // Verify no provider clients were created in the database
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should handle single restaurant ID correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const restaurantIds = [restaurantId];

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Verify no provider clients exist before the operation
            const clientsBeforeOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsBeforeOperation).toHaveLength(0);

            await useCase.execute({ body: mockEventBody });

            // Verify provider client was created in the database
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(1);

            const client = clientsAfterOperation[0];
            expect(client.restaurantId).toBe(restaurantId);
            expect(client.providerClientId).toBe('test-customer-uuid');
            expect(client.source).toBe(ProviderClientSource.LAFOURCHETTE);
            expect(client.firstName).toBe('John');
            expect(client.lastName).toBe('Doe');
            expect(client.email).toBe('<EMAIL>');

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
        });

        it('should handle empty restaurant IDs array', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantIds: string[] = [];

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');

            // Verify no provider clients were created in the database
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should handle different event types correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBodyUpdated = createMockEventBody({
                eventType: TheForkEventType.CUSTOMER_UPDATED,
                uuid: 'updated-customer-uuid',
            });
            const mockTheForkClient = createMockTheForkClient({ customerUuid: 'updated-customer-uuid' });
            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const restaurantIds = [restaurantId];

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Verify no provider clients exist before the operation
            const clientsBeforeOperation = await providerClientsRepository.getProviderClientsByProviderClientId('updated-customer-uuid');
            expect(clientsBeforeOperation).toHaveLength(0);

            await useCase.execute({ body: mockEventBodyUpdated });

            // Verify provider client was created in the database
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('updated-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(1);

            const client = clientsAfterOperation[0];
            expect(client.restaurantId).toBe(restaurantId);
            expect(client.providerClientId).toBe('updated-customer-uuid');
            expect(client.source).toBe(ProviderClientSource.LAFOURCHETTE);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('updated-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
        });

        it('should handle upsert operations correctly with real repository', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const restaurantIds = [restaurantId];

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Execute the use case twice to test upsert behavior
            await useCase.execute({ body: mockEventBody });

            // Verify first execution created the client
            const clientsAfterFirstExecution = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterFirstExecution).toHaveLength(1);

            // Execute again with updated data
            const updatedTheForkClient = createMockTheForkClient({
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
            });
            theForkServiceMock.fetchClient.mockResolvedValue(updatedTheForkClient);

            await useCase.execute({ body: mockEventBody });

            // Verify upsert behavior - should still be 1 client but with updated data
            const clientsAfterSecondExecution = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterSecondExecution).toHaveLength(1);

            const updatedClient = clientsAfterSecondExecution[0];
            expect(updatedClient.firstName).toBe('Jane');
            expect(updatedClient.lastName).toBe('Smith');
            expect(updatedClient.email).toBe('<EMAIL>');

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledTimes(2);
        });
    });
});
