import { container } from 'tsyringe';

import { ProviderClientSource } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkCivility, TheForkClient } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/thefork.service';
import { HandleTheForkUpsertedCustomerUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-customer/handle-thefork-upserted-customer.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import {
    TheForkEntityType,
    TheForkEventRequestBody,
    TheForkEventType,
} from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';

// Mock the logger module
jest.mock(':helpers/logger', () => ({
    logger: {
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    },
}));

describe('HandleTheForkUpsertedCustomerUseCase', () => {
    let theForkServiceMock: jest.Mocked<TheForkService>;
    let providerClientsRepositoryMock: jest.Mocked<ProviderClientsRepository>;
    let loggerMock: jest.Mocked<typeof import(':helpers/logger').logger>;

    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['RestaurantsRepository']);

        // Mock TheForkService
        theForkServiceMock = {
            fetchClient: jest.fn(),
            fetchClientList: jest.fn(),
            fetchReservation: jest.fn(),
            fetchReservationList: jest.fn(),
            mapRestaurantIdToTheForkGroupUuid: jest.fn(),
            mapRestaurantIdToTheForkRestaurantUuid: jest.fn(),
            mapTheForkRestaurantUuidToRestaurantIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkService>;

        // Mock ProviderClientsRepository
        providerClientsRepositoryMock = {
            upsertProviderClient: jest.fn(),
            getProviderClientsByProviderClientId: jest.fn(),
        } as unknown as jest.Mocked<ProviderClientsRepository>;

        // Get the mocked logger
        loggerMock = jest.mocked(logger);

        // Clear all mock calls
        jest.clearAllMocks();

        container.register(TheForkService, { useValue: theForkServiceMock });
        container.register(ProviderClientsRepository, { useValue: providerClientsRepositoryMock });
    });

    const createMockTheForkClient = (overrides: Partial<TheForkClient> = {}): TheForkClient => ({
        customerUuid: 'test-customer-uuid',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        birthDate: '1990-01-01',
        locale: 'fr_FR',
        civility: TheForkCivility.MR,
        rank: 'gold',
        computedRank: 'gold',
        isVip: false,
        address: '123 Test Street',
        allergiesAndIntolerances: null,
        dietaryRestrictions: null,
        favFood: null,
        favDrinks: null,
        favSeating: null,
        notes: null,
        originRestaurantUuid: 'origin-restaurant-uuid',
        originRestaurantName: 'Origin Restaurant',
        creationDate: '2023-01-01T00:00:00Z',
        lastUpdateAt: '2023-01-01T00:00:00Z',
        isPromoter: false,
        secondaryPhone: null,
        country: 'France',
        city: 'Paris',
        zipcode: '75001',
        optins: {
            restaurantNewsletter: true,
        },
        customFields: null,
        ...overrides,
    });

    const createMockEventBody = (overrides: Partial<TheForkEventRequestBody> = {}): TheForkEventRequestBody => ({
        entityType: TheForkEntityType.CUSTOMER,
        eventType: TheForkEventType.CUSTOMER_CREATED,
        uuid: 'test-customer-uuid',
        ...overrides,
    });

    const createMockProviderClient = (restaurantId: string, customerUuid: string): ProviderClient => {
        return new ProviderClient({
            id: 'mock-provider-client-id',
            providerClientId: customerUuid,
            restaurantId,
            source: ProviderClientSource.LAFOURCHETTE,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            visits: [],
        });
    };

    describe('execute', () => {
        it('should successfully upsert provider clients when TheFork client is found with origin restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantIds = ['restaurant-1', 'restaurant-2'];
            const mockProviderClient1 = createMockProviderClient('restaurant-1', 'test-customer-uuid');
            const mockProviderClient2 = createMockProviderClient('restaurant-2', 'test-customer-uuid');

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);
            providerClientsRepositoryMock.upsertProviderClient
                .mockResolvedValueOnce(mockProviderClient1)
                .mockResolvedValueOnce(mockProviderClient2);

            // Mock ProviderClient.fromTheForkClient static method
            const fromTheForkClientSpy = jest.spyOn(ProviderClient, 'fromTheForkClient');
            fromTheForkClientSpy.mockReturnValueOnce(mockProviderClient1).mockReturnValueOnce(mockProviderClient2);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
            expect(fromTheForkClientSpy).toHaveBeenCalledTimes(2);
            expect(fromTheForkClientSpy).toHaveBeenNthCalledWith(1, mockTheForkClient, 'restaurant-1');
            expect(fromTheForkClientSpy).toHaveBeenNthCalledWith(2, mockTheForkClient, 'restaurant-2');
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenCalledTimes(2);
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenNthCalledWith(1, mockProviderClient1);
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenNthCalledWith(2, mockProviderClient2);

            fromTheForkClientSpy.mockRestore();
        });

        it('should return early and log error when TheFork client is not found', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            theForkServiceMock.fetchClient.mockResolvedValue(null);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Customer not found', {
                customerUuid: 'test-customer-uuid',
            });
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).not.toHaveBeenCalled();
            expect(providerClientsRepositoryMock.upsertProviderClient).not.toHaveBeenCalled();
        });

        it('should return early and log error when TheFork client has no origin restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient({ originRestaurantUuid: null });
            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Customer has no origin restaurant', {
                customerUuid: 'test-customer-uuid',
            });
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).not.toHaveBeenCalled();
            expect(providerClientsRepositoryMock.upsertProviderClient).not.toHaveBeenCalled();
        });

        it('should handle single restaurant ID correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantIds = ['single-restaurant-id'];
            const mockProviderClient = createMockProviderClient('single-restaurant-id', 'test-customer-uuid');

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);
            providerClientsRepositoryMock.upsertProviderClient.mockResolvedValue(mockProviderClient);

            // Mock ProviderClient.fromTheForkClient static method
            const fromTheForkClientSpy = jest.spyOn(ProviderClient, 'fromTheForkClient');
            fromTheForkClientSpy.mockReturnValue(mockProviderClient);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
            expect(fromTheForkClientSpy).toHaveBeenCalledTimes(1);
            expect(fromTheForkClientSpy).toHaveBeenCalledWith(mockTheForkClient, 'single-restaurant-id');
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenCalledTimes(1);
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenCalledWith(mockProviderClient);

            fromTheForkClientSpy.mockRestore();
        });

        it('should handle empty restaurant IDs array', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantIds: string[] = [];

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
            expect(providerClientsRepositoryMock.upsertProviderClient).not.toHaveBeenCalled();
        });

        it('should handle different event types correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBodyUpdated = createMockEventBody({
                eventType: TheForkEventType.CUSTOMER_UPDATED,
                uuid: 'updated-customer-uuid',
            });
            const mockTheForkClient = createMockTheForkClient({ customerUuid: 'updated-customer-uuid' });
            const restaurantIds = ['restaurant-1'];
            const mockProviderClient = createMockProviderClient('restaurant-1', 'updated-customer-uuid');

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);
            providerClientsRepositoryMock.upsertProviderClient.mockResolvedValue(mockProviderClient);

            // Mock ProviderClient.fromTheForkClient static method
            const fromTheForkClientSpy = jest.spyOn(ProviderClient, 'fromTheForkClient');
            fromTheForkClientSpy.mockReturnValue(mockProviderClient);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);
            await useCase.execute({ body: mockEventBodyUpdated });

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('updated-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenCalledTimes(1);

            fromTheForkClientSpy.mockRestore();
        });

        it('should handle repository errors gracefully', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockTheForkClient = createMockTheForkClient();
            const restaurantIds = ['restaurant-1'];
            const mockProviderClient = createMockProviderClient('restaurant-1', 'test-customer-uuid');

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);
            providerClientsRepositoryMock.upsertProviderClient.mockRejectedValue(new Error('Database error'));

            // Mock ProviderClient.fromTheForkClient static method
            const fromTheForkClientSpy = jest.spyOn(ProviderClient, 'fromTheForkClient');
            fromTheForkClientSpy.mockReturnValue(mockProviderClient);

            const useCase = container.resolve(HandleTheForkUpsertedCustomerUseCase);

            await expect(useCase.execute({ body: mockEventBody })).rejects.toThrow('Database error');

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(providerClientsRepositoryMock.upsertProviderClient).toHaveBeenCalledTimes(1);

            fromTheForkClientSpy.mockRestore();
        });
    });
});
