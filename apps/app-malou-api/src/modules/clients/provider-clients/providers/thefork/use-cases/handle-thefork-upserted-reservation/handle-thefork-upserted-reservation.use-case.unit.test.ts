import { container } from 'tsyringe';

import { logger } from ':helpers/logger';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkCivility, TheForkClient, TheForkReservation } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { HandleTheForkUpsertedReservationUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/handle-thefork-upserted-reservation/handle-thefork-upserted-reservation.use-case';
import { getDefaultProviderClient } from ':modules/clients/provider-clients/tests/provider-client.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import {
    TheForkEntityType,
    TheForkEventRequestBody,
    TheForkEventType,
} from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';

// Mock the logger module
jest.mock(':helpers/logger', () => ({
    logger: {
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    },
}));

describe('HandleTheForkUpsertedReservationUseCase', () => {
    let theForkServiceMock: jest.Mocked<TheForkService>;
    let loggerMock: jest.Mocked<typeof import(':helpers/logger').logger>;

    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['RestaurantsRepository', 'ProviderClientsRepository']);

        // Mock TheForkService
        theForkServiceMock = {
            fetchClient: jest.fn(),
            fetchClientList: jest.fn(),
            fetchReservation: jest.fn(),
            fetchReservationList: jest.fn(),
            mapRestaurantIdToTheForkGroupUuid: jest.fn(),
            mapRestaurantIdToTheForkRestaurantUuid: jest.fn(),
            mapTheForkRestaurantUuidToRestaurantIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkService>;

        // Get the mocked logger
        loggerMock = jest.mocked(logger);

        // Clear all mock calls
        jest.clearAllMocks();

        container.register(TheForkService, { useValue: theForkServiceMock });
    });

    const createMockTheForkClient = (overrides: Partial<TheForkClient> = {}): TheForkClient => ({
        customerUuid: 'test-customer-uuid',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        birthDate: '1990-01-01',
        locale: 'fr_FR',
        civility: TheForkCivility.MR,
        rank: 'gold',
        computedRank: 'gold',
        isVip: false,
        address: '123 Test Street',
        allergiesAndIntolerances: null,
        dietaryRestrictions: null,
        favFood: null,
        favDrinks: null,
        favSeating: null,
        notes: null,
        originRestaurantUuid: 'origin-restaurant-uuid',
        originRestaurantName: 'Origin Restaurant',
        creationDate: '2023-01-01T00:00:00Z',
        lastUpdateAt: '2023-01-01T00:00:00Z',
        isPromoter: false,
        secondaryPhone: null,
        country: 'France',
        city: 'Paris',
        zipcode: '75001',
        optins: {
            restaurantNewsletter: true,
        },
        customFields: null,
        ...overrides,
    });

    const createMockTheForkReservation = (overrides: Partial<TheForkReservation> = {}): TheForkReservation => ({
        reservationUuid: 'test-reservation-uuid',
        restaurantUuid: 'test-restaurant-uuid',
        mealDate: '2023-12-25T19:00:00Z',
        partySize: 4,
        status: 'CONFIRMED' as any,
        offerUuid: null,
        customerNote: null,
        customerUuid: 'test-customer-uuid',
        customFields: null,
        offerDetails: null,
        utmTrackingInformation: null,
        billAmount: null,
        reservationChannel: 'TheFork' as any,
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        ...overrides,
    });

    const createMockEventBody = (overrides: Partial<TheForkEventRequestBody> = {}): TheForkEventRequestBody => ({
        entityType: TheForkEntityType.RESERVATION,
        eventType: TheForkEventType.RESERVATION_CREATED,
        uuid: 'test-reservation-uuid',
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully create provider visit when reservation is found and customer exists', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());

            const mockEventBody = createMockEventBody();
            const mockReservation = createMockTheForkReservation();
            const mockClient = createMockTheForkClient();

            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);
            theForkServiceMock.fetchClient.mockResolvedValue(mockClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Verify no provider clients exist before the operation
            const clientsBeforeOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsBeforeOperation).toHaveLength(0);

            await useCase.execute({ body: mockEventBody });

            // Verify provider clients were created with visits
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(2);

            // Verify each client has the visit
            for (const client of clientsAfterOperation) {
                expect(client.visits).toHaveLength(1);
                const visit = client.visits[0];
                expect(visit.providerVisitId).toBe('test-reservation-uuid');
                expect(visit.visitDate).toEqual(new Date('2023-12-25T19:00:00Z'));
                expect(visit.providerVisitFields).toEqual(mockReservation);
            }

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('test-reservation-uuid');
            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds).toHaveBeenCalledWith('origin-restaurant-uuid');
        });

        it('should successfully add visit to existing provider clients', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'providerClients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },
                    providerClients: {
                        data(dependencies) {
                            return [
                                getDefaultProviderClient()
                                    .providerClientId('test-customer-uuid')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .firstName('John')
                                    .lastName('Doe')
                                    .email('<EMAIL>')
                                    .visits([])
                                    .build(),
                                getDefaultProviderClient()
                                    .providerClientId('test-customer-uuid')
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .firstName('John')
                                    .lastName('Doe')
                                    .email('<EMAIL>')
                                    .visits([])
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockReservation = createMockTheForkReservation();

            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Verify clients exist before the operation (seeded by testCase)
            const clientsBeforeOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsBeforeOperation).toHaveLength(2);
            expect(clientsBeforeOperation.every((c) => c.visits.length === 0)).toBe(true);

            await useCase.execute({ body: mockEventBody });

            // Verify visits were added to existing clients
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(2);

            // Verify each client now has the visit
            for (const client of clientsAfterOperation) {
                expect(client.visits).toHaveLength(1);
                const visit = client.visits[0];
                expect(visit.providerVisitId).toBe('test-reservation-uuid');
                expect(visit.visitDate).toEqual(new Date('2023-12-25T19:00:00Z'));
                expect(visit.providerVisitFields).toEqual(mockReservation);
            }

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('test-reservation-uuid');
            expect(theForkServiceMock.fetchClient).not.toHaveBeenCalled();
        });

        it('should return early and log error when reservation is not found', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            theForkServiceMock.fetchReservation.mockResolvedValue(null);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('test-reservation-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Reservation not found', {
                reservationUuid: 'test-reservation-uuid',
            });
            expect(theForkServiceMock.fetchClient).not.toHaveBeenCalled();

            // Verify no provider clients were created
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should return early and log error when reservation has no customer', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockReservation = createMockTheForkReservation({ customerUuid: null });
            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('test-reservation-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Reservation has no customer', {
                reservationUuid: 'test-reservation-uuid',
            });
            expect(theForkServiceMock.fetchClient).not.toHaveBeenCalled();

            // Verify no provider clients were created
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should handle customer not found when no existing provider clients', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockReservation = createMockTheForkReservation();
            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);
            theForkServiceMock.fetchClient.mockResolvedValue(null);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('test-reservation-uuid');
            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Customer not found', {
                customerUuid: 'test-customer-uuid',
            });

            // Verify no provider clients were created
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should handle customer with no origin restaurant when no existing provider clients', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();

            const mockEventBody = createMockEventBody();
            const mockReservation = createMockTheForkReservation();
            const mockClient = createMockTheForkClient({ originRestaurantUuid: null });
            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);
            theForkServiceMock.fetchClient.mockResolvedValue(mockClient);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            await useCase.execute({ body: mockEventBody });

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('test-reservation-uuid');
            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith('test-customer-uuid');
            expect(loggerMock.error).toHaveBeenCalledWith('[WEBHOOKS] [THEFORK] - Customer has no origin restaurant', {
                customerUuid: 'test-customer-uuid',
            });

            // Verify no provider clients were created
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(0);
        });

        it('should handle different event types correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const mockEventBodyUpdated = createMockEventBody({
                eventType: TheForkEventType.RESERVATION_UPDATED,
                uuid: 'updated-reservation-uuid',
            });
            const mockReservation = createMockTheForkReservation({
                reservationUuid: 'updated-reservation-uuid',
                partySize: 6,
                status: 'CANCELED' as any,
            });
            const mockClient = createMockTheForkClient();
            const restaurantIds = [restaurantId];

            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);
            theForkServiceMock.fetchClient.mockResolvedValue(mockClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            await useCase.execute({ body: mockEventBodyUpdated });

            // Verify provider client was created with the updated reservation
            const clientsAfterOperation = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterOperation).toHaveLength(1);

            const client = clientsAfterOperation[0];
            expect(client.visits).toHaveLength(1);
            const visit = client.visits[0];
            expect(visit.providerVisitId).toBe('updated-reservation-uuid');
            expect(visit.providerVisitFields.partySize).toBe(6);
            expect(visit.providerVisitFields.status).toBe('CANCELED');

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledWith('updated-reservation-uuid');
        });

        it('should upsert visits correctly when same reservation is processed multiple times', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const mockEventBody = createMockEventBody();
            const mockReservation = createMockTheForkReservation();
            const mockClient = createMockTheForkClient();
            const restaurantIds = [restaurantId];

            theForkServiceMock.fetchReservation.mockResolvedValue(mockReservation);
            theForkServiceMock.fetchClient.mockResolvedValue(mockClient);
            theForkServiceMock.mapTheForkRestaurantUuidToRestaurantIds.mockReturnValue(restaurantIds);

            const useCase = container.resolve(HandleTheForkUpsertedReservationUseCase);
            const providerClientsRepository = container.resolve(ProviderClientsRepository);

            // Execute the use case first time
            await useCase.execute({ body: mockEventBody });

            // Verify first execution created the client with visit
            const clientsAfterFirstExecution = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterFirstExecution).toHaveLength(1);
            expect(clientsAfterFirstExecution[0].visits).toHaveLength(1);

            // Execute again with updated reservation data
            const updatedReservation = createMockTheForkReservation({
                partySize: 8,
                status: 'CONFIRMED' as any,
                customerNote: 'Updated note',
            });
            theForkServiceMock.fetchReservation.mockResolvedValue(updatedReservation);

            await useCase.execute({ body: mockEventBody });

            // Verify upsert behavior - should still be 1 client with 1 visit but updated data
            const clientsAfterSecondExecution = await providerClientsRepository.getProviderClientsByProviderClientId('test-customer-uuid');
            expect(clientsAfterSecondExecution).toHaveLength(1);
            expect(clientsAfterSecondExecution[0].visits).toHaveLength(1);

            const updatedVisit = clientsAfterSecondExecution[0].visits[0];
            expect(updatedVisit.providerVisitFields.partySize).toBe(8);
            expect(updatedVisit.providerVisitFields.customerNote).toBe('Updated note');

            expect(theForkServiceMock.fetchReservation).toHaveBeenCalledTimes(2);
        });
    });
});
