import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/thefork.service';
import { TheForkEventRequestBody } from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';

@singleton()
export class HandleTheForkUpsertedCustomerUseCase {
    constructor(
        private readonly _theForkService: TheForkService,
        private readonly _providerClientsRepository: ProviderClientsRepository
    ) {}

    async execute({ body }: { body: TheForkEventRequestBody }): Promise<void> {
        const theForkClient = await this._theForkService.fetchClient(body.uuid);
        if (!theForkClient) {
            logger.error('[WEBHOOKS] [THEFORK] - Customer not found', {
                customerUuid: body.uuid,
            });
            return;
        }
        if (!theForkClient.originRestaurantUuid) {
            logger.error('[WEBHOOKS] [THEFORK] - Customer has no origin restaurant', {
                customerUuid: body.uuid,
            });
            return;
        }

        const restaurantIds = this._theForkService.mapTheForkRestaurantUuidToRestaurantIds(theForkClient.originRestaurantUuid);

        const upsertedProviderClients = await Promise.all(
            restaurantIds.map((restaurantId) => {
                const providerClient = ProviderClient.fromTheForkClient(theForkClient, restaurantId);
                return this._providerClientsRepository.upsertProviderClient(providerClient);
            })
        );

        await Promise.all(upsertedProviderClients);
    }
}
