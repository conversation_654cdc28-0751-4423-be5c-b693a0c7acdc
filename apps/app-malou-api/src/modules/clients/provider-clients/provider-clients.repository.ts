import { omit } from 'lodash';
import { singleton } from 'tsyringe';

import { EntityRepository, IProviderClient, ProviderClientModel, toDbId } from '@malou-io/package-models';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { Address } from ':modules/restaurants/entities/address.entity';

@singleton()
export default class ProviderClientsRepository extends EntityRepository<IProviderClient> {
    constructor() {
        super(ProviderClientModel);
    }

    async upsertProviderClient(providerClient: ProviderClient): Promise<ProviderClient> {
        const filter = { providerClientId: providerClient.providerClientId, restaurantId: toDbId(providerClient.restaurantId) };

        const document = this._toDocument(providerClient);
        const update = omit(document, '_id');

        const upsertedDocument = await this.upsert({
            filter,
            update,
            options: {
                lean: true,
            },
        });
        return this._toEntity(upsertedDocument);
    }

    async getProviderClientsByProviderClientId(providerClientId: string): Promise<ProviderClient[]> {
        const documents = await this.find({
            filter: { providerClientId },
            options: { lean: true },
        });
        return documents.map((document) => this._toEntity(document));
    }

    private _toDocument(entity: ProviderClient): IProviderClient {
        return {
            _id: toDbId(entity.id),
            providerClientId: entity.providerClientId,
            restaurantId: toDbId(entity.restaurantId),
            source: entity.source,
            firstName: entity.firstName,
            lastName: entity.lastName,
            birthday: entity.birthday,
            address: entity.address,
            email: entity.email,
            phone: entity.phone,
            language: entity.language,
            civility: entity.civility,
            contactOptions: entity.contactOptions,
            visits: entity.visits.map((visit) => ({
                providerVisitId: visit.providerVisitId,
                restaurantId: toDbId(visit.restaurantId),
                visitDate: visit.visitDate,
                providerVisitFields: visit.providerVisitFields,
            })),
        };
    }

    private _toEntity(document: IProviderClient): ProviderClient {
        return new ProviderClient({
            id: document._id.toString(),
            providerClientId: document.providerClientId,
            restaurantId: document.restaurantId.toString(),
            source: document.source,
            firstName: document.firstName,
            lastName: document.lastName,
            birthday: document.birthday,
            address: document.address
                ? new Address({
                      locality: document.address.locality ?? undefined,
                      country: document.address.country,
                      postalCode: document.address.postalCode ?? undefined,
                      formattedAddress: document.address.formattedAddress ?? undefined,
                      administrativeArea: document.address.administrativeArea ?? undefined,
                      route: document.address.route ?? undefined,
                      streetNumber: document.address.streetNumber ?? undefined,
                      regionCode: document.address.regionCode,
                  })
                : undefined,
            email: document.email,
            phone: document.phone,
            language: document.language ?? undefined,
            civility: document.civility ?? undefined,
            contactOptions: document.contactOptions,
            visits: document.visits.map(
                (visit) =>
                    new ProviderVisit({
                        providerVisitId: visit.providerVisitId,
                        restaurantId: visit.restaurantId.toString(),
                        visitDate: visit.visitDate,
                        providerVisitFields: visit.providerVisitFields,
                    })
            ),
        });
    }
}
