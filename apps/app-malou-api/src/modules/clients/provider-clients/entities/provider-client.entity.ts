import { newDbId } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    ContactMode,
    EntityConstructor,
    ProviderClientCivility,
    ProviderClientPhone,
    ProviderClientSource,
} from '@malou-io/package-utils';

import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { TheForkClient } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import {
    mapTheForkCivilityToProviderClientCivility,
    mapTheForkOptinsToProviderClientContactOptions,
    mapTheForkPhoneToProviderClientPhone,
} from ':modules/clients/provider-clients/providers/thefork/thefork.mapper';
import { Address } from ':modules/restaurants/entities/address.entity';

export type ProviderClientProps = EntityConstructor<ProviderClient> & { id: string };

export class ProviderClient {
    id: string;
    providerClientId: string;
    restaurantId: string;
    source: ProviderClientSource;
    firstName?: string;
    lastName?: string;
    birthday?: Date;
    address?: Address;
    email?: string;
    phone?: ProviderClientPhone;
    language?: ApplicationLanguage;
    civility?: ProviderClientCivility;
    contactOptions?: ContactMode[];
    visits: ProviderVisit[];

    constructor(data: ProviderClientProps) {
        this.id = data.id;
        this.providerClientId = data.providerClientId;
        this.restaurantId = data.restaurantId;
        this.source = data.source;
        this.firstName = data.firstName;
        this.lastName = data.lastName;
        this.birthday = data.birthday;
        this.address = data.address;
        this.email = data.email;
        this.phone = data.phone;
        this.language = data.language;
        this.civility = data.civility;
        this.contactOptions = data.contactOptions;
        this.visits = data.visits;
    }

    static fromTheForkClient(data: TheForkClient, restaurantId: string): ProviderClient {
        return new ProviderClient({
            id: newDbId().toString(),
            providerClientId: data.customerUuid,
            restaurantId,
            source: ProviderClientSource.LAFOURCHETTE,
            firstName: data.firstName ?? undefined, // supposed to be required but not the case in the test API
            lastName: data.lastName ?? undefined, // supposed to be required but not the case in the test API
            birthday: data.birthDate ? new Date(data.birthDate) : undefined,
            address: undefined, // TODO map address, need to know the exact format (in the test API all the fields are null)
            email: data.email ?? undefined, // supposed to be required but not the case in the test API
            phone: mapTheForkPhoneToProviderClientPhone(data.phone),
            civility: mapTheForkCivilityToProviderClientCivility(data.civility),
            contactOptions: mapTheForkOptinsToProviderClientContactOptions(data.optins),
            visits: [],
        });
    }

    upsertProviderVisit(providerVisit: ProviderVisit) {
        const existingVisitIndex = this.visits.findIndex((visit) => visit.providerVisitId === providerVisit.providerVisitId);
        if (existingVisitIndex !== -1) {
            this.visits[existingVisitIndex] = providerVisit;
        } else {
            this.visits.push(providerVisit);
        }
    }
}
