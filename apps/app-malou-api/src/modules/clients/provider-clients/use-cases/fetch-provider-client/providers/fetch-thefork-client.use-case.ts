import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { IFetchProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/fetch-provider-client.use-case';

@singleton()
export class FetchTheForkClientUseCase implements IFetchProviderClientUseCase {
    constructor(private readonly _theForkService: TheForkService) {}

    async execute(providerClientId: string, restaurantId: string): Promise<ProviderClient> {
        const theForkClient = await this._theForkService.fetchClient(providerClientId);
        if (!theForkClient) {
            throw new MalouError(MalouErrorCode.THEFORK_CLIENT_NOT_FOUND, {
                metadata: {
                    providerClientId,
                    restaurantId,
                },
            });
        }
        return ProviderClient.fromTheForkClient(theForkClient, restaurantId);
    }
}
