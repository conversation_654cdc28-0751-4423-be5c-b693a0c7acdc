import { container } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { FetchProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/fetch-provider-client.use-case';
import { FetchTheForkClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/fetch-thefork-client.use-case';

describe('FetchProviderClientUseCase', () => {
    let fetchTheForkClientUseCaseMock: jest.Mocked<FetchTheForkClientUseCase>;

    beforeEach(() => {
        container.clearInstances();

        // Mock FetchTheForkClientUseCase
        fetchTheForkClientUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<FetchTheForkClientUseCase>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(FetchTheForkClientUseCase, fetchTheForkClientUseCaseMock);
    });

    const createMockProviderClient = (overrides: Partial<ProviderClient> = {}): ProviderClient => {
        return new ProviderClient({
            id: 'test-client-id',
            providerClientId: 'test-provider-client-id',
            restaurantId: 'test-restaurant-id',
            source: 'lafourchette' as any,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            visits: [],
            ...overrides,
        });
    };

    describe('execute', () => {
        it('should successfully fetch provider client for LAFOURCHETTE platform', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClient = createMockProviderClient({
                providerClientId,
                restaurantId,
                firstName: 'John',
                lastName: 'Doe',
            });

            fetchTheForkClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(FetchProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId, platformKey);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
            expect(result).toEqual(mockProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.firstName).toBe('John');
            expect(result.lastName).toBe('Doe');
        });

        it('should throw MalouError for unsupported platform key', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.FACEBOOK; // Unsupported platform

            const useCase = container.resolve(FetchProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.FACEBOOK,
                    },
                })
            );

            expect(fetchTheForkClientUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for GMB platform key', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.GMB; // Unsupported platform

            const useCase = container.resolve(FetchProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.GMB,
                    },
                })
            );

            expect(fetchTheForkClientUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for INSTAGRAM platform key', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.INSTAGRAM; // Unsupported platform

            const useCase = container.resolve(FetchProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.INSTAGRAM,
                    },
                })
            );

            expect(fetchTheForkClientUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for TRIPADVISOR platform key', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.TRIPADVISOR; // Unsupported platform

            const useCase = container.resolve(FetchProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.TRIPADVISOR,
                    },
                })
            );

            expect(fetchTheForkClientUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should propagate errors from TheFork use case', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;
            const mockError = new MalouError(MalouErrorCode.THEFORK_CLIENT_NOT_FOUND, {
                metadata: { providerClientId, restaurantId },
            });

            fetchTheForkClientUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId, platformKey)).rejects.toThrow(mockError);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
        });

        it('should handle different provider client IDs correctly', async () => {
            const providerClientId = 'different-client-id';
            const restaurantId = 'different-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClient = createMockProviderClient({
                providerClientId,
                restaurantId,
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
            });

            fetchTheForkClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(FetchProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId, platformKey);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
            expect(result).toEqual(mockProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.firstName).toBe('Jane');
            expect(result.lastName).toBe('Smith');
            expect(result.email).toBe('<EMAIL>');
        });

        it('should handle provider client with visits correctly', async () => {
            const providerClientId = 'client-with-visits';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClient = createMockProviderClient({
                providerClientId,
                restaurantId,
                firstName: 'Alice',
                lastName: 'Johnson',
                visits: [
                    {
                        id: 'visit-1',
                        providerVisitId: 'reservation-1',
                        visitDate: new Date('2023-01-15'),
                        providerVisitFields: { reservationUuid: 'reservation-1' },
                    } as any,
                ],
            });

            fetchTheForkClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(FetchProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId, platformKey);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
            expect(result).toEqual(mockProviderClient);
            expect(result.visits).toHaveLength(1);
            expect(result.visits[0].providerVisitId).toBe('reservation-1');
        });

        it('should handle provider client with minimal data correctly', async () => {
            const providerClientId = 'minimal-client';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClient = createMockProviderClient({
                providerClientId,
                restaurantId,
                firstName: undefined,
                lastName: undefined,
                email: undefined,
                phone: undefined,
                visits: [],
            });

            fetchTheForkClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(FetchProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId, platformKey);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
            expect(result).toEqual(mockProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBeUndefined();
            expect(result.phone).toBeUndefined();
            expect(result.visits).toEqual([]);
        });

        it('should handle generic errors from TheFork use case', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;
            const mockError = new Error('Generic TheFork service error');

            fetchTheForkClientUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId, platformKey)).rejects.toThrow(mockError);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
        });

        it('should handle long provider client IDs correctly', async () => {
            const providerClientId = 'very-long-provider-client-id-with-many-characters-12345678901234567890';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClient = createMockProviderClient({
                providerClientId,
                restaurantId,
            });

            fetchTheForkClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(FetchProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId, platformKey);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
            expect(result.providerClientId).toBe(providerClientId);
        });

        it('should handle special characters in provider client ID correctly', async () => {
            const providerClientId = 'client-with-special-chars-@#$%^&*()';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClient = createMockProviderClient({
                providerClientId,
                restaurantId,
            });

            fetchTheForkClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(FetchProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId, platformKey);

            expect(fetchTheForkClientUseCaseMock.execute).toHaveBeenCalledWith(providerClientId, restaurantId);
            expect(result.providerClientId).toBe(providerClientId);
        });
    });
});
