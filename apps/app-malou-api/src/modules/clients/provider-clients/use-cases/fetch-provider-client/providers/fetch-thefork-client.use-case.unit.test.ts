import { container } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkCivility, TheForkClient } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { FetchTheForkClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/fetch-thefork-client.use-case';

describe('FetchTheForkClientUseCase', () => {
    let theForkServiceMock: jest.Mocked<TheForkService>;

    beforeEach(() => {
        container.clearInstances();

        // Mock TheForkService
        theForkServiceMock = {
            fetchClient: jest.fn(),
            fetchClientList: jest.fn(),
            fetchReservation: jest.fn(),
            fetchReservationList: jest.fn(),
            mapRestaurantIdToTheForkGroupUuid: jest.fn(),
            mapRestaurantIdToTheForkRestaurantUuid: jest.fn(),
            mapTheForkRestaurantUuidToRestaurantIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkService>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(TheForkService, theForkServiceMock);
    });

    const createMockTheForkClient = (overrides: Partial<TheForkClient> = {}): TheForkClient => ({
        customerUuid: 'test-customer-uuid',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        birthDate: '1990-01-01',
        locale: 'fr_FR',
        civility: TheForkCivility.MR,
        rank: 'gold',
        computedRank: 'gold',
        isVip: false,
        address: '123 Test Street',
        allergiesAndIntolerances: null,
        dietaryRestrictions: null,
        favFood: null,
        favDrinks: null,
        favSeating: null,
        notes: null,
        originRestaurantUuid: 'origin-restaurant-uuid',
        originRestaurantName: 'Origin Restaurant',
        creationDate: '2023-01-01T00:00:00Z',
        lastUpdateAt: '2023-01-01T00:00:00Z',
        isPromoter: false,
        secondaryPhone: null,
        country: 'France',
        city: 'Paris',
        zipcode: '75001',
        optins: {
            restaurantNewsletter: true,
        },
        customFields: null,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert TheFork client to ProviderClient', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('John');
            expect(result.lastName).toBe('Doe');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe('lafourchette');
            expect(result.visits).toEqual([]);
        });

        it('should throw MalouError when TheFork client is not found', async () => {
            const providerClientId = 'non-existent-client';
            const restaurantId = 'test-restaurant-id';

            theForkServiceMock.fetchClient.mockResolvedValue(null);

            const useCase = container.resolve(FetchTheForkClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.THEFORK_CLIENT_NOT_FOUND,
                    metadata: {
                        providerClientId,
                        restaurantId,
                    },
                })
            );

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
        });

        it('should handle TheFork client with null/undefined fields correctly', async () => {
            const providerClientId = 'client-with-nulls';
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: null,
                lastName: null,
                email: null,
                phone: null,
                birthDate: null,
                address: null,
                civility: null,
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBeUndefined();
            expect(result.phone).toBeUndefined();
            expect(result.birthday).toBeUndefined();
            expect(result.address).toBeUndefined();
            expect(result.civility).toBeUndefined();
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe('lafourchette');
            expect(result.visits).toEqual([]);
        });

        it('should handle TheFork client with complete data correctly', async () => {
            const providerClientId = 'complete-client';
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phone: '+33987654321',
                birthDate: '1985-05-15',
                civility: TheForkCivility.MS,
                address: '456 Main Street',
                country: 'France',
                city: 'Lyon',
                zipcode: '69001',
                optins: {
                    restaurantNewsletter: false,
                },
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Alice');
            expect(result.lastName).toBe('Johnson');
            expect(result.email).toBe('<EMAIL>');
            expect(result.birthday).toEqual(new Date('1985-05-15'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe('lafourchette');
            expect(result.visits).toEqual([]);
        });

        it('should handle different provider client IDs correctly', async () => {
            const providerClientId = 'different-client-id';
            const restaurantId = 'different-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: 'Bob',
                lastName: 'Wilson',
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Bob');
            expect(result.lastName).toBe('Wilson');
            expect(result.restaurantId).toBe(restaurantId);
        });

        it('should propagate errors from TheFork service', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('TheFork service error');

            theForkServiceMock.fetchClient.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchTheForkClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(mockError);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
        });

        it('should handle long provider client IDs correctly', async () => {
            const providerClientId = 'very-long-provider-client-id-with-many-characters-12345678901234567890';
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: 'LongId',
                lastName: 'Client',
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('LongId');
            expect(result.lastName).toBe('Client');
        });

        it('should handle special characters in provider client ID correctly', async () => {
            const providerClientId = 'client-with-special-chars-@#$%^&*()';
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: 'Special',
                lastName: 'Character',
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Special');
            expect(result.lastName).toBe('Character');
        });

        it('should preserve all TheFork client data during conversion', async () => {
            const providerClientId = 'detailed-client';
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: providerClientId,
                firstName: 'Detailed',
                lastName: 'Client',
                email: '<EMAIL>',
                phone: '+33123456789',
                birthDate: '1990-12-25',
                civility: TheForkCivility.MR,
                rank: 'platinum',
                isVip: true,
                address: '789 Premium Street',
                country: 'France',
                city: 'Nice',
                zipcode: '06000',
                optins: {
                    restaurantNewsletter: true,
                },
            });

            theForkServiceMock.fetchClient.mockResolvedValue(mockTheForkClient);

            const useCase = container.resolve(FetchTheForkClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Detailed');
            expect(result.lastName).toBe('Client');
            expect(result.email).toBe('<EMAIL>');
            expect(result.birthday).toEqual(new Date('1990-12-25'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe('lafourchette');
            expect(result.visits).toEqual([]);
        });

        it('should handle undefined TheFork client response', async () => {
            const providerClientId = 'undefined-client';
            const restaurantId = 'test-restaurant-id';

            theForkServiceMock.fetchClient.mockResolvedValue(undefined as any);

            const useCase = container.resolve(FetchTheForkClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.THEFORK_CLIENT_NOT_FOUND,
                    metadata: {
                        providerClientId,
                        restaurantId,
                    },
                })
            );

            expect(theForkServiceMock.fetchClient).toHaveBeenCalledWith(providerClientId);
        });
    });
});
