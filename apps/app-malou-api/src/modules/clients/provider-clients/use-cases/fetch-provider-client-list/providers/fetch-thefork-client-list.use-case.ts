import { singleton } from 'tsyringe';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { IFetchProviderClientListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-list/fetch-provider-client-list.use-case';

@singleton()
export class FetchTheForkClientListUseCase implements IFetchProviderClientListUseCase {
    constructor(private readonly _theForkService: TheForkService) {}

    async execute(startDate: Date, endDate: Date, restaurantId: string): Promise<ProviderClient[]> {
        const theForkClientList = await this._theForkService.fetchClientList(startDate, endDate, restaurantId);
        return theForkClientList.map((theForkClient) => ProviderClient.fromTheForkClient(theForkClient, restaurantId));
    }
}
