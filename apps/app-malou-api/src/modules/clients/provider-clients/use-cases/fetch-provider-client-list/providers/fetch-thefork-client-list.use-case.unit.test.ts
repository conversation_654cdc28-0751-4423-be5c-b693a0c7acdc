import { container } from 'tsyringe';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkCivility, TheForkClient } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { FetchTheForkClientListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-list/providers/fetch-thefork-client-list.use-case';

describe('FetchTheForkClientListUseCase', () => {
    let theForkServiceMock: jest.Mocked<TheForkService>;

    beforeEach(() => {
        container.clearInstances();

        // Mock TheForkService
        theForkServiceMock = {
            fetchClientList: jest.fn(),
            fetchClient: jest.fn(),
            fetchReservation: jest.fn(),
            fetchReservationList: jest.fn(),
            mapRestaurantIdToTheForkGroupUuid: jest.fn(),
            mapRestaurantIdToTheForkRestaurantUuid: jest.fn(),
            mapTheForkRestaurantUuidToRestaurantIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkService>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(TheForkService, theForkServiceMock);
    });

    const createMockTheForkClient = (overrides: Partial<TheForkClient> = {}): TheForkClient => ({
        customerUuid: 'test-customer-uuid',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        birthDate: '1990-01-01',
        locale: 'fr_FR',
        civility: TheForkCivility.MR,
        rank: 'gold',
        computedRank: 'gold',
        isVip: false,
        address: '123 Test Street',
        allergiesAndIntolerances: null,
        dietaryRestrictions: null,
        favFood: null,
        favDrinks: null,
        favSeating: null,
        notes: null,
        originRestaurantUuid: 'origin-restaurant-uuid',
        originRestaurantName: 'Origin Restaurant',
        creationDate: '2023-01-01T00:00:00Z',
        lastUpdateAt: '2023-01-01T00:00:00Z',
        isPromoter: false,
        secondaryPhone: null,
        country: 'France',
        city: 'Paris',
        zipcode: '75001',
        optins: {
            restaurantNewsletter: true,
        },
        customFields: null,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert TheFork clients to ProviderClients', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClients = [
                createMockTheForkClient({ customerUuid: 'client-1', firstName: 'John', lastName: 'Doe' }),
                createMockTheForkClient({ customerUuid: 'client-2', firstName: 'Jane', lastName: 'Smith' }),
            ];

            theForkServiceMock.fetchClientList.mockResolvedValue(mockTheForkClients);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(2);
            expect(result[0]).toBeInstanceOf(ProviderClient);
            expect(result[1]).toBeInstanceOf(ProviderClient);

            // Verify first client conversion
            expect(result[0].providerClientId).toBe('client-1');
            expect(result[0].firstName).toBe('John');
            expect(result[0].lastName).toBe('Doe');
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].source).toBe('lafourchette');
            expect(result[0].visits).toEqual([]);

            // Verify second client conversion
            expect(result[1].providerClientId).toBe('client-2');
            expect(result[1].firstName).toBe('Jane');
            expect(result[1].lastName).toBe('Smith');
            expect(result[1].restaurantId).toBe(restaurantId);
            expect(result[1].source).toBe('lafourchette');
            expect(result[1].visits).toEqual([]);
        });

        it('should return empty array when TheFork service returns empty array', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            theForkServiceMock.fetchClientList.mockResolvedValue([]);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });

        it('should handle single TheFork client correctly', async () => {
            const startDate = new Date('2023-06-01');
            const endDate = new Date('2023-06-30');
            const restaurantId = 'single-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: 'single-client',
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phone: '+33987654321',
            });

            theForkServiceMock.fetchClientList.mockResolvedValue([mockTheForkClient]);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderClient);
            expect(result[0].providerClientId).toBe('single-client');
            expect(result[0].firstName).toBe('Alice');
            expect(result[0].lastName).toBe('Johnson');
            expect(result[0].email).toBe('<EMAIL>');
            expect(result[0].restaurantId).toBe(restaurantId);
        });

        it('should handle large number of TheFork clients', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            // Create 50 mock clients
            const mockTheForkClients = Array.from({ length: 50 }, (_, index) =>
                createMockTheForkClient({
                    customerUuid: `client-${index}`,
                    firstName: `Client${index}`,
                    lastName: `LastName${index}`,
                    email: `client${index}@example.com`,
                })
            );

            theForkServiceMock.fetchClientList.mockResolvedValue(mockTheForkClients);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(50);

            // Verify all clients are converted correctly
            result.forEach((providerClient, index) => {
                expect(providerClient).toBeInstanceOf(ProviderClient);
                expect(providerClient.providerClientId).toBe(`client-${index}`);
                expect(providerClient.firstName).toBe(`Client${index}`);
                expect(providerClient.lastName).toBe(`LastName${index}`);
                expect(providerClient.email).toBe(`client${index}@example.com`);
                expect(providerClient.restaurantId).toBe(restaurantId);
                expect(providerClient.source).toBe('lafourchette');
                expect(providerClient.visits).toEqual([]);
            });
        });

        it('should handle TheFork clients with null/undefined fields correctly', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: 'client-with-nulls',
                firstName: null,
                lastName: null,
                email: null,
                phone: null,
                birthDate: null,
                address: null,
                civility: null,
            });

            theForkServiceMock.fetchClientList.mockResolvedValue([mockTheForkClient]);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderClient);
            expect(result[0].providerClientId).toBe('client-with-nulls');
            expect(result[0].firstName).toBeUndefined();
            expect(result[0].lastName).toBeUndefined();
            expect(result[0].email).toBeUndefined();
            expect(result[0].phone).toBeUndefined();
            expect(result[0].birthday).toBeUndefined();
            expect(result[0].address).toBeUndefined();
            expect(result[0].civility).toBeUndefined();
            expect(result[0].restaurantId).toBe(restaurantId);
        });

        it('should propagate errors from TheFork service', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('TheFork service error');

            theForkServiceMock.fetchClientList.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchTheForkClientListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId)).rejects.toThrow(mockError);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
        });

        it('should handle different date ranges correctly', async () => {
            const startDate = new Date('2023-12-01');
            const endDate = new Date('2023-12-31');
            const restaurantId = 'december-restaurant';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: 'december-client',
                firstName: 'December',
                lastName: 'Client',
            });

            theForkServiceMock.fetchClientList.mockResolvedValue([mockTheForkClient]);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchClientList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0].providerClientId).toBe('december-client');
            expect(result[0].firstName).toBe('December');
            expect(result[0].lastName).toBe('Client');
            expect(result[0].restaurantId).toBe(restaurantId);
        });

        it('should preserve all TheFork client data during conversion', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkClient = createMockTheForkClient({
                customerUuid: 'detailed-client',
                firstName: 'Detailed',
                lastName: 'Client',
                email: '<EMAIL>',
                phone: '+33123456789',
                birthDate: '1985-05-15',
                civility: TheForkCivility.MS,
                optins: {
                    restaurantNewsletter: false,
                },
            });

            theForkServiceMock.fetchClientList.mockResolvedValue([mockTheForkClient]);

            const useCase = container.resolve(FetchTheForkClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(result).toHaveLength(1);
            const providerClient = result[0];

            expect(providerClient.providerClientId).toBe('detailed-client');
            expect(providerClient.firstName).toBe('Detailed');
            expect(providerClient.lastName).toBe('Client');
            expect(providerClient.email).toBe('<EMAIL>');
            expect(providerClient.birthday).toEqual(new Date('1985-05-15'));
            expect(providerClient.restaurantId).toBe(restaurantId);
            expect(providerClient.source).toBe('lafourchette');
            expect(providerClient.visits).toEqual([]);
        });
    });
});
