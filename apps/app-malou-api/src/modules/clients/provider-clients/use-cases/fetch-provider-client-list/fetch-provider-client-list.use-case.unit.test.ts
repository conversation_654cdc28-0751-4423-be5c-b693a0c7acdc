import { container } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { FetchProviderClientListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-list/fetch-provider-client-list.use-case';
import { FetchTheForkClientListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-list/providers/fetch-thefork-client-list.use-case';

describe('FetchProviderClientListUseCase', () => {
    let fetchTheForkClientListUseCaseMock: jest.Mocked<FetchTheForkClientListUseCase>;

    beforeEach(() => {
        container.clearInstances();

        // Mock FetchTheForkClientListUseCase
        fetchTheForkClientListUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<FetchTheForkClientListUseCase>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(FetchTheForkClientListUseCase, fetchTheForkClientListUseCaseMock);
    });

    const createMockProviderClient = (overrides: Partial<ProviderClient> = {}): ProviderClient => {
        return new ProviderClient({
            id: 'test-client-id',
            providerClientId: 'test-provider-client-id',
            restaurantId: 'test-restaurant-id',
            source: 'lafourchette' as any,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            visits: [],
            ...overrides,
        });
    };

    describe('execute', () => {
        it('should successfully fetch provider client list for LAFOURCHETTE platform', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClients = [
                createMockProviderClient({ id: 'client-1', firstName: 'John' }),
                createMockProviderClient({ id: 'client-2', firstName: 'Jane' }),
            ];

            fetchTheForkClientListUseCaseMock.execute.mockResolvedValue(mockProviderClients);

            const useCase = container.resolve(FetchProviderClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkClientListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual(mockProviderClients);
            expect(result).toHaveLength(2);
            expect(result[0].firstName).toBe('John');
            expect(result[1].firstName).toBe('Jane');
        });

        it('should return empty array when no provider clients are found', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            fetchTheForkClientListUseCaseMock.execute.mockResolvedValue([]);

            const useCase = container.resolve(FetchProviderClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkClientListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });

        it('should propagate errors from TheFork use case', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;
            const mockError = new Error('TheFork service error');

            fetchTheForkClientListUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchProviderClientListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId, platformKey)).rejects.toThrow(mockError);

            expect(fetchTheForkClientListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
        });

        it('should handle different date ranges correctly', async () => {
            const startDate = new Date('2023-06-01');
            const endDate = new Date('2023-06-30');
            const restaurantId = 'different-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderClients = [createMockProviderClient({ restaurantId: 'different-restaurant-id' })];

            fetchTheForkClientListUseCaseMock.execute.mockResolvedValue(mockProviderClients);

            const useCase = container.resolve(FetchProviderClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkClientListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual(mockProviderClients);
            expect(result[0].restaurantId).toBe('different-restaurant-id');
        });

        it('should handle large number of provider clients', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            // Create a large array of mock provider clients
            const mockProviderClients = Array.from({ length: 100 }, (_, index) =>
                createMockProviderClient({
                    id: `client-${index}`,
                    firstName: `Client${index}`,
                    providerClientId: `provider-client-${index}`,
                })
            );

            fetchTheForkClientListUseCaseMock.execute.mockResolvedValue(mockProviderClients);

            const useCase = container.resolve(FetchProviderClientListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkClientListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual(mockProviderClients);
            expect(result).toHaveLength(100);
            expect(result[0].firstName).toBe('Client0');
            expect(result[99].firstName).toBe('Client99');
        });
    });
});
