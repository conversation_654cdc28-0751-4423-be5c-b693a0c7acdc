import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { IFetchProviderVisitUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit/fetch-provider-visit.use-case';

@singleton()
export class FetchTheForkVisitUseCase implements IFetchProviderVisitUseCase {
    constructor(private readonly _theForkService: TheForkService) {}

    async execute(reservationId: string, restaurantId: string): Promise<ProviderVisit> {
        const theForkVisit = await this._theForkService.fetchReservation(reservationId, restaurantId);
        if (!theForkVisit) {
            throw new MalouError(MalouErrorCode.THEFORK_VISIT_NOT_FOUND, {
                metadata: {
                    reservationId,
                    restaurantId,
                },
            });
        }
        return ProviderVisit.fromTheForkVisit(theForkVisit, restaurantId);
    }
}
