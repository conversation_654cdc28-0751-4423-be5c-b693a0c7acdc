import { container } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { FetchProviderVisitUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit/fetch-provider-visit.use-case';
import { FetchTheForkVisitUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit/providers/fetch-thefork-visit.use-case';

describe('FetchProviderVisitUseCase', () => {
    let fetchTheForkVisitUseCaseMock: jest.Mocked<FetchTheForkVisitUseCase>;

    beforeEach(() => {
        container.clearInstances();

        // Mock FetchTheForkVisitUseCase
        fetchTheForkVisitUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<FetchTheForkVisitUseCase>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(FetchTheForkVisitUseCase, fetchTheForkVisitUseCaseMock);
    });

    const createMockProviderVisit = (overrides: Partial<ProviderVisit> = {}): ProviderVisit => {
        return new ProviderVisit({
            providerVisitId: 'test-visit-id',
            restaurantId: 'test-restaurant-id',
            visitDate: new Date('2023-12-25T19:00:00Z'),
            providerVisitFields: {
                reservationUuid: 'test-visit-id',
                restaurantUuid: 'test-restaurant-uuid',
                mealDate: '2023-12-25T19:00:00Z',
                partySize: 4,
                status: 'CONFIRMED',
                customerNote: null,
                billAmount: null,
            },
            ...overrides,
        });
    };

    describe('execute', () => {
        it('should successfully fetch provider visit for LAFOURCHETTE platform', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
                visitDate: new Date('2023-12-25T19:00:00Z'),
                providerVisitFields: {
                    reservationUuid: visitId,
                    partySize: 4,
                    status: 'CONFIRMED',
                    customerNote: 'Birthday celebration',
                },
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
            expect(result).toEqual(mockProviderVisit);
            expect(result.providerVisitId).toBe(visitId);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.visitDate).toEqual(new Date('2023-12-25T19:00:00Z'));
            expect(result.providerVisitFields.customerNote).toBe('Birthday celebration');
        });

        it('should throw MalouError for unsupported platform key', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.FACEBOOK; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.FACEBOOK,
                    },
                })
            );

            expect(fetchTheForkVisitUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for GMB platform key', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.GMB; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.GMB,
                    },
                })
            );

            expect(fetchTheForkVisitUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for INSTAGRAM platform key', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.INSTAGRAM; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.INSTAGRAM,
                    },
                })
            );

            expect(fetchTheForkVisitUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for TRIPADVISOR platform key', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.TRIPADVISOR; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.TRIPADVISOR,
                    },
                })
            );

            expect(fetchTheForkVisitUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should propagate errors from TheFork use case', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;
            const mockError = new MalouError(MalouErrorCode.THEFORK_VISIT_NOT_FOUND, {
                metadata: { reservationId: visitId, restaurantId },
            });

            fetchTheForkVisitUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId, platformKey)).rejects.toThrow(mockError);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
        });

        it('should handle different visit IDs correctly', async () => {
            const visitId = 'different-visit-id';
            const restaurantId = 'different-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
                visitDate: new Date('2023-06-15T20:00:00Z'),
                providerVisitFields: {
                    reservationUuid: visitId,
                    partySize: 6,
                    status: 'CONFIRMED',
                    customerNote: 'Anniversary dinner',
                    billAmount: { totalPrice: 250.75, currency: 'EUR' },
                },
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
            expect(result).toEqual(mockProviderVisit);
            expect(result.providerVisitId).toBe(visitId);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.visitDate).toEqual(new Date('2023-06-15T20:00:00Z'));
            expect(result.providerVisitFields.customerNote).toBe('Anniversary dinner');
            expect(result.providerVisitFields.billAmount.totalPrice).toBe(250.75);
        });

        it('should handle provider visit with minimal data correctly', async () => {
            const visitId = 'minimal-visit';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
                visitDate: new Date('2023-01-01T12:00:00Z'),
                providerVisitFields: {
                    reservationUuid: visitId,
                    partySize: 2,
                    status: 'CONFIRMED',
                    customerNote: null,
                    billAmount: null,
                },
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
            expect(result).toEqual(mockProviderVisit);
            expect(result.providerVisitId).toBe(visitId);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.providerVisitFields.customerNote).toBeNull();
            expect(result.providerVisitFields.billAmount).toBeNull();
        });

        it('should handle provider visit with complex visit fields correctly', async () => {
            const visitId = 'complex-visit';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
                visitDate: new Date('2023-08-20T19:30:00Z'),
                providerVisitFields: {
                    reservationUuid: visitId,
                    restaurantUuid: 'restaurant-uuid-123',
                    mealDate: '2023-08-20T19:30:00Z',
                    partySize: 8,
                    status: 'CONFIRMED',
                    offerUuid: 'special-offer-456',
                    customerNote: 'Corporate dinner - vegetarian options needed',
                    customerUuid: 'customer-789',
                    billAmount: { totalPrice: 480.25, currency: 'EUR' },
                    reservationChannel: 'TheFork',
                    createdAt: '2023-08-15T10:00:00Z',
                    updatedAt: '2023-08-18T14:30:00Z',
                },
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
            expect(result).toEqual(mockProviderVisit);
            expect(result.providerVisitFields.partySize).toBe(8);
            expect(result.providerVisitFields.customerNote).toBe('Corporate dinner - vegetarian options needed');
            expect(result.providerVisitFields.billAmount.totalPrice).toBe(480.25);
            expect(result.providerVisitFields.offerUuid).toBe('special-offer-456');
        });

        it('should handle generic errors from TheFork use case', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;
            const mockError = new Error('Generic TheFork service error');

            fetchTheForkVisitUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId, platformKey)).rejects.toThrow(mockError);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
        });

        it('should handle long visit IDs correctly', async () => {
            const visitId = 'very-long-visit-id-with-many-characters-12345678901234567890';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
            expect(result.providerVisitId).toBe(visitId);
        });

        it('should handle special characters in visit ID correctly', async () => {
            const visitId = 'visit-with-special-chars-@#$%^&*()';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(fetchTheForkVisitUseCaseMock.execute).toHaveBeenCalledWith(visitId, restaurantId);
            expect(result.providerVisitId).toBe(visitId);
        });

        it('should handle provider visit with different statuses correctly', async () => {
            const visitId = 'canceled-visit';
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisit = createMockProviderVisit({
                providerVisitId: visitId,
                restaurantId,
                providerVisitFields: {
                    reservationUuid: visitId,
                    status: 'CANCELED',
                    partySize: 4,
                    customerNote: 'Canceled due to weather',
                },
            });

            fetchTheForkVisitUseCaseMock.execute.mockResolvedValue(mockProviderVisit);

            const useCase = container.resolve(FetchProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId, platformKey);

            expect(result.providerVisitFields.status).toBe('CANCELED');
            expect(result.providerVisitFields.customerNote).toBe('Canceled due to weather');
        });
    });
});
