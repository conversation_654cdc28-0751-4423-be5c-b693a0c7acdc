import { inject, singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { FetchTheForkVisitListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/providers/fetch-thefork-visit-list.use-case';

export interface IFetchProviderVisitsUseCase {
    execute(startDate: Date, endDate: Date, restaurantId: string): Promise<ProviderVisit[]>;
}

@singleton()
export class FetchProviderVisitListUseCase {
    constructor(@inject(FetchTheForkVisitListUseCase) private readonly _fetchTheForkVisitListUseCase: IFetchProviderVisitsUseCase) {}

    async execute(startDate: Date, endDate: Date, restaurantId: string, platformKey: PlatformKey): Promise<ProviderVisit[]> {
        let fetchProviderVisitsUseCase: IFetchProviderVisitsUseCase;
        switch (platformKey) {
            case PlatformKey.LAFOURCHETTE:
                fetchProviderVisitsUseCase = this._fetchTheForkVisitListUseCase;
                break;
            default:
                throw new MalouError(MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY, {
                    metadata: {
                        platformKey,
                    },
                });
        }
        return fetchProviderVisitsUseCase.execute(startDate, endDate, restaurantId);
    }
}
