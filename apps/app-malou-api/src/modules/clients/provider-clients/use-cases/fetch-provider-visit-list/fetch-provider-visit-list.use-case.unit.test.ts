import { container } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { FetchProviderVisitListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/fetch-provider-visit-list.use-case';
import { FetchTheForkVisitListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/providers/fetch-thefork-visit-list.use-case';

describe('FetchProviderVisitListUseCase', () => {
    let fetchTheForkVisitListUseCaseMock: jest.Mocked<FetchTheForkVisitListUseCase>;

    beforeEach(() => {
        container.clearInstances();

        // Mock FetchTheForkVisitListUseCase
        fetchTheForkVisitListUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<FetchTheForkVisitListUseCase>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(FetchTheForkVisitListUseCase, fetchTheForkVisitListUseCaseMock);
    });

    const createMockProviderVisit = (overrides: Partial<ProviderVisit> = {}): ProviderVisit => {
        return new ProviderVisit({
            providerVisitId: 'test-visit-id',
            restaurantId: 'test-restaurant-id',
            visitDate: new Date('2023-12-25T19:00:00Z'),
            providerVisitFields: {
                reservationUuid: 'test-visit-id',
                restaurantUuid: 'test-restaurant-uuid',
                mealDate: '2023-12-25T19:00:00Z',
                partySize: 4,
                status: 'CONFIRMED',
            },
            ...overrides,
        });
    };

    describe('execute', () => {
        it('should successfully fetch provider visit list for LAFOURCHETTE platform', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisits = [
                createMockProviderVisit({ providerVisitId: 'visit-1', visitDate: new Date('2023-01-15T19:00:00Z') }),
                createMockProviderVisit({ providerVisitId: 'visit-2', visitDate: new Date('2023-01-20T20:00:00Z') }),
            ];

            fetchTheForkVisitListUseCaseMock.execute.mockResolvedValue(mockProviderVisits);

            const useCase = container.resolve(FetchProviderVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkVisitListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual(mockProviderVisits);
            expect(result).toHaveLength(2);
            expect(result[0].providerVisitId).toBe('visit-1');
            expect(result[1].providerVisitId).toBe('visit-2');
        });

        it('should return empty array when no provider visits are found', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            fetchTheForkVisitListUseCaseMock.execute.mockResolvedValue([]);

            const useCase = container.resolve(FetchProviderVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkVisitListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });

        it('should throw MalouError for unsupported platform key', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.FACEBOOK; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.FACEBOOK,
                    },
                })
            );

            expect(fetchTheForkVisitListUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for GMB platform key', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.GMB; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.GMB,
                    },
                })
            );

            expect(fetchTheForkVisitListUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should throw MalouError for INSTAGRAM platform key', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.INSTAGRAM; // Unsupported platform

            const useCase = container.resolve(FetchProviderVisitListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId, platformKey)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY,
                    metadata: {
                        platformKey: PlatformKey.INSTAGRAM,
                    },
                })
            );

            expect(fetchTheForkVisitListUseCaseMock.execute).not.toHaveBeenCalled();
        });

        it('should propagate errors from TheFork use case', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;
            const mockError = new Error('TheFork service error');

            fetchTheForkVisitListUseCaseMock.execute.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchProviderVisitListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId, platformKey)).rejects.toThrow(mockError);

            expect(fetchTheForkVisitListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
        });

        it('should handle different date ranges correctly', async () => {
            const startDate = new Date('2023-06-01');
            const endDate = new Date('2023-06-30');
            const restaurantId = 'different-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisits = [
                createMockProviderVisit({
                    providerVisitId: 'june-visit',
                    restaurantId: 'different-restaurant-id',
                    visitDate: new Date('2023-06-15T19:00:00Z'),
                }),
            ];

            fetchTheForkVisitListUseCaseMock.execute.mockResolvedValue(mockProviderVisits);

            const useCase = container.resolve(FetchProviderVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkVisitListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual(mockProviderVisits);
            expect(result[0].restaurantId).toBe('different-restaurant-id');
            expect(result[0].providerVisitId).toBe('june-visit');
        });

        it('should handle large number of provider visits', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            // Create a large array of mock provider visits
            const mockProviderVisits = Array.from({ length: 100 }, (_, index) =>
                createMockProviderVisit({
                    providerVisitId: `visit-${index}`,
                    visitDate: new Date(`2023-01-${String((index % 28) + 1).padStart(2, '0')}T19:00:00Z`),
                    providerVisitFields: {
                        reservationUuid: `visit-${index}`,
                        partySize: (index % 8) + 1,
                        status: index % 2 === 0 ? 'CONFIRMED' : 'CANCELED',
                    },
                })
            );

            fetchTheForkVisitListUseCaseMock.execute.mockResolvedValue(mockProviderVisits);

            const useCase = container.resolve(FetchProviderVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkVisitListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual(mockProviderVisits);
            expect(result).toHaveLength(100);
            expect(result[0].providerVisitId).toBe('visit-0');
            expect(result[99].providerVisitId).toBe('visit-99');
        });

        it('should handle provider visits with different visit dates', async () => {
            const startDate = new Date('2023-12-01');
            const endDate = new Date('2023-12-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisits = [
                createMockProviderVisit({
                    providerVisitId: 'morning-visit',
                    visitDate: new Date('2023-12-15T12:00:00Z'),
                    providerVisitFields: { mealDate: '2023-12-15T12:00:00Z', partySize: 2 },
                }),
                createMockProviderVisit({
                    providerVisitId: 'evening-visit',
                    visitDate: new Date('2023-12-15T20:00:00Z'),
                    providerVisitFields: { mealDate: '2023-12-15T20:00:00Z', partySize: 6 },
                }),
            ];

            fetchTheForkVisitListUseCaseMock.execute.mockResolvedValue(mockProviderVisits);

            const useCase = container.resolve(FetchProviderVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(fetchTheForkVisitListUseCaseMock.execute).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(2);
            expect(result[0].visitDate).toEqual(new Date('2023-12-15T12:00:00Z'));
            expect(result[1].visitDate).toEqual(new Date('2023-12-15T20:00:00Z'));
            expect(result[0].providerVisitFields.partySize).toBe(2);
            expect(result[1].providerVisitFields.partySize).toBe(6);
        });

        it('should handle provider visits with complex visit fields', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const platformKey = PlatformKey.LAFOURCHETTE;

            const mockProviderVisits = [
                createMockProviderVisit({
                    providerVisitId: 'complex-visit',
                    providerVisitFields: {
                        reservationUuid: 'complex-visit',
                        restaurantUuid: 'restaurant-uuid',
                        mealDate: '2023-01-15T19:00:00Z',
                        partySize: 4,
                        status: 'CONFIRMED',
                        customerNote: 'Birthday celebration',
                        customerUuid: 'customer-123',
                        billAmount: { totalPrice: 150.5, currency: 'EUR' },
                        reservationChannel: 'TheFork',
                    },
                }),
            ];

            fetchTheForkVisitListUseCaseMock.execute.mockResolvedValue(mockProviderVisits);

            const useCase = container.resolve(FetchProviderVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId, platformKey);

            expect(result).toHaveLength(1);
            expect(result[0].providerVisitFields.customerNote).toBe('Birthday celebration');
            expect(result[0].providerVisitFields.billAmount.totalPrice).toBe(150.5);
            expect(result[0].providerVisitFields.reservationChannel).toBe('TheFork');
        });
    });
});
