import { singleton } from 'tsyringe';

import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/thefork.service';
import { IFetchProviderVisitsUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/fetch-provider-visit-list.use-case';

@singleton()
export class FetchTheForkVisitListUseCase implements IFetchProviderVisitsUseCase {
    constructor(private readonly _theForkService: TheForkService) {}

    async execute(startDate: Date, endDate: Date, restaurantId: string): Promise<ProviderVisit[]> {
        const theForkVisits = await this._theForkService.fetchReservationList(startDate, endDate, restaurantId);
        return theForkVisits.map((theForkVisit) => ProviderVisit.fromTheForkVisit(theForkVisit, restaurantId));
    }
}
