import { container } from 'tsyringe';

import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkReservation } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { FetchTheForkVisitListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit-list/providers/fetch-thefork-visit-list.use-case';

describe('FetchTheForkVisitListUseCase', () => {
    let theForkServiceMock: jest.Mocked<TheForkService>;

    beforeEach(() => {
        container.clearInstances();

        // Mock TheForkService
        theForkServiceMock = {
            fetchClient: jest.fn(),
            fetchClientList: jest.fn(),
            fetchReservation: jest.fn(),
            fetchReservationList: jest.fn(),
            mapRestaurantIdToTheForkGroupUuid: jest.fn(),
            mapRestaurantIdToTheForkRestaurantUuid: jest.fn(),
            mapTheForkRestaurantUuidToRestaurantIds: jest.fn(),
        } as unknown as jest.Mocked<TheForkService>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(TheForkService, theForkServiceMock);
    });

    const createMockTheForkReservation = (overrides: Partial<TheForkReservation> = {}): TheForkReservation => ({
        reservationUuid: 'test-reservation-uuid',
        restaurantUuid: 'test-restaurant-uuid',
        mealDate: '2023-12-25T19:00:00Z',
        partySize: 4,
        status: 'CONFIRMED' as any,
        offerUuid: null,
        customerNote: null,
        customerUuid: 'test-customer-uuid',
        customFields: null,
        offerDetails: null,
        utmTrackingInformation: null,
        billAmount: null,
        reservationChannel: 'TheFork' as any,
        createdAt: '2023-12-01T10:00:00Z',
        updatedAt: '2023-12-01T10:00:00Z',
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert TheFork reservations to ProviderVisits', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkReservations = [
                createMockTheForkReservation({
                    reservationUuid: 'reservation-1',
                    mealDate: '2023-01-15T19:00:00Z',
                    partySize: 2,
                }),
                createMockTheForkReservation({
                    reservationUuid: 'reservation-2',
                    mealDate: '2023-01-20T20:00:00Z',
                    partySize: 6,
                }),
            ];

            theForkServiceMock.fetchReservationList.mockResolvedValue(mockTheForkReservations);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(2);
            expect(result[0]).toBeInstanceOf(ProviderVisit);
            expect(result[1]).toBeInstanceOf(ProviderVisit);

            // Verify first visit conversion
            expect(result[0].providerVisitId).toBe('reservation-1');
            expect(result[0].visitDate).toEqual(new Date('2023-01-15T19:00:00Z'));
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].providerVisitFields).toEqual(mockTheForkReservations[0]);

            // Verify second visit conversion
            expect(result[1].providerVisitId).toBe('reservation-2');
            expect(result[1].visitDate).toEqual(new Date('2023-01-20T20:00:00Z'));
            expect(result[1].restaurantId).toBe(restaurantId);
            expect(result[1].providerVisitFields).toEqual(mockTheForkReservations[1]);
        });

        it('should return empty array when TheFork service returns empty array', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            theForkServiceMock.fetchReservationList.mockResolvedValue([]);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });

        it('should handle single TheFork reservation correctly', async () => {
            const startDate = new Date('2023-06-01');
            const endDate = new Date('2023-06-30');
            const restaurantId = 'single-restaurant-id';

            const mockTheForkReservation = createMockTheForkReservation({
                reservationUuid: 'single-reservation',
                mealDate: '2023-06-15T19:00:00Z',
                partySize: 4,
                status: 'CONFIRMED' as any,
                customerNote: 'Birthday celebration',
            });

            theForkServiceMock.fetchReservationList.mockResolvedValue([mockTheForkReservation]);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderVisit);
            expect(result[0].providerVisitId).toBe('single-reservation');
            expect(result[0].visitDate).toEqual(new Date('2023-06-15T19:00:00Z'));
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].providerVisitFields.customerNote).toBe('Birthday celebration');
        });

        it('should handle large number of TheFork reservations', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            // Create 50 mock reservations
            const mockTheForkReservations = Array.from({ length: 50 }, (_, index) =>
                createMockTheForkReservation({
                    reservationUuid: `reservation-${index}`,
                    mealDate: `2023-01-${String((index % 28) + 1).padStart(2, '0')}T19:00:00Z`,
                    partySize: (index % 8) + 1,
                    status: index % 2 === 0 ? ('CONFIRMED' as any) : ('CANCELED' as any),
                })
            );

            theForkServiceMock.fetchReservationList.mockResolvedValue(mockTheForkReservations);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(50);

            // Verify all reservations are converted correctly
            result.forEach((providerVisit, index) => {
                expect(providerVisit).toBeInstanceOf(ProviderVisit);
                expect(providerVisit.providerVisitId).toBe(`reservation-${index}`);
                expect(providerVisit.restaurantId).toBe(restaurantId);
                expect(providerVisit.providerVisitFields.partySize).toBe((index % 8) + 1);
                expect(providerVisit.providerVisitFields.status).toBe(index % 2 === 0 ? 'CONFIRMED' : 'CANCELED');
            });
        });

        it('should handle TheFork reservations with complete data correctly', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkReservation = createMockTheForkReservation({
                reservationUuid: 'complete-reservation',
                restaurantUuid: 'restaurant-uuid-123',
                mealDate: '2023-01-15T20:30:00Z',
                partySize: 6,
                status: 'CONFIRMED' as any,
                offerUuid: 'offer-123',
                customerNote: 'Anniversary dinner',
                customerUuid: 'customer-456',
                billAmount: {
                    totalPrice: 250.75,
                    currency: 'EUR',
                },
                reservationChannel: 'TheFork' as any,
                createdAt: '2023-01-10T14:30:00Z',
                updatedAt: '2023-01-12T16:45:00Z',
            });

            theForkServiceMock.fetchReservationList.mockResolvedValue([mockTheForkReservation]);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderVisit);
            expect(result[0].providerVisitId).toBe('complete-reservation');
            expect(result[0].visitDate).toEqual(new Date('2023-01-15T20:30:00Z'));
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].providerVisitFields).toEqual(mockTheForkReservation);
            expect(result[0].providerVisitFields.billAmount.totalPrice).toBe(250.75);
            expect(result[0].providerVisitFields.customerNote).toBe('Anniversary dinner');
        });

        it('should handle TheFork reservations with null/undefined fields correctly', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkReservation = createMockTheForkReservation({
                reservationUuid: 'minimal-reservation',
                mealDate: '2023-01-15T19:00:00Z',
                partySize: 2,
                status: 'CONFIRMED' as any,
                offerUuid: null,
                customerNote: null,
                customerUuid: null,
                customFields: null,
                offerDetails: null,
                utmTrackingInformation: null,
                billAmount: null,
                updatedAt: null,
            });

            theForkServiceMock.fetchReservationList.mockResolvedValue([mockTheForkReservation]);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderVisit);
            expect(result[0].providerVisitId).toBe('minimal-reservation');
            expect(result[0].visitDate).toEqual(new Date('2023-01-15T19:00:00Z'));
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].providerVisitFields.offerUuid).toBeNull();
            expect(result[0].providerVisitFields.customerNote).toBeNull();
            expect(result[0].providerVisitFields.billAmount).toBeNull();
        });

        it('should propagate errors from TheFork service', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('TheFork service error');

            theForkServiceMock.fetchReservationList.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);

            await expect(useCase.execute(startDate, endDate, restaurantId)).rejects.toThrow(mockError);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
        });

        it('should handle different date ranges correctly', async () => {
            const startDate = new Date('2023-12-01');
            const endDate = new Date('2023-12-31');
            const restaurantId = 'december-restaurant';

            const mockTheForkReservation = createMockTheForkReservation({
                reservationUuid: 'december-reservation',
                mealDate: '2023-12-25T19:00:00Z',
                partySize: 8,
                status: 'CONFIRMED' as any,
            });

            theForkServiceMock.fetchReservationList.mockResolvedValue([mockTheForkReservation]);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(theForkServiceMock.fetchReservationList).toHaveBeenCalledWith(startDate, endDate, restaurantId);
            expect(result).toHaveLength(1);
            expect(result[0].providerVisitId).toBe('december-reservation');
            expect(result[0].visitDate).toEqual(new Date('2023-12-25T19:00:00Z'));
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].providerVisitFields.partySize).toBe(8);
        });

        it('should preserve all TheFork reservation data during conversion', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkReservation = createMockTheForkReservation({
                reservationUuid: 'detailed-reservation',
                restaurantUuid: 'detailed-restaurant-uuid',
                mealDate: '2023-01-20T19:30:00Z',
                partySize: 4,
                status: 'CONFIRMED' as any,
                offerUuid: 'special-offer-123',
                customerNote: 'Vegetarian options needed',
                customerUuid: 'loyal-customer-789',
                billAmount: {
                    totalPrice: 180.25,
                    currency: 'EUR',
                },
                reservationChannel: 'TheFork' as any,
                createdAt: '2023-01-15T10:00:00Z',
                updatedAt: '2023-01-18T14:30:00Z',
            });

            theForkServiceMock.fetchReservationList.mockResolvedValue([mockTheForkReservation]);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(result).toHaveLength(1);
            const providerVisit = result[0];

            expect(providerVisit.providerVisitId).toBe('detailed-reservation');
            expect(providerVisit.visitDate).toEqual(new Date('2023-01-20T19:30:00Z'));
            expect(providerVisit.restaurantId).toBe(restaurantId);
            expect(providerVisit.providerVisitFields).toEqual(mockTheForkReservation);
            expect(providerVisit.providerVisitFields.customerNote).toBe('Vegetarian options needed');
            expect(providerVisit.providerVisitFields.billAmount.totalPrice).toBe(180.25);
            expect(providerVisit.providerVisitFields.offerUuid).toBe('special-offer-123');
        });

        it('should handle reservations with different statuses correctly', async () => {
            const startDate = new Date('2023-01-01');
            const endDate = new Date('2023-01-31');
            const restaurantId = 'test-restaurant-id';

            const mockTheForkReservations = [
                createMockTheForkReservation({
                    reservationUuid: 'confirmed-reservation',
                    status: 'CONFIRMED' as any,
                    partySize: 4,
                }),
                createMockTheForkReservation({
                    reservationUuid: 'canceled-reservation',
                    status: 'CANCELED' as any,
                    partySize: 2,
                }),
                createMockTheForkReservation({
                    reservationUuid: 'no-show-reservation',
                    status: 'NO_SHOW' as any,
                    partySize: 6,
                }),
            ];

            theForkServiceMock.fetchReservationList.mockResolvedValue(mockTheForkReservations);

            const useCase = container.resolve(FetchTheForkVisitListUseCase);
            const result = await useCase.execute(startDate, endDate, restaurantId);

            expect(result).toHaveLength(3);
            expect(result[0].providerVisitFields.status).toBe('CONFIRMED');
            expect(result[1].providerVisitFields.status).toBe('CANCELED');
            expect(result[2].providerVisitFields.status).toBe('NO_SHOW');
        });
    });
});
