import {
    ApplicationLanguage,
    capitalize,
    ContactMode,
    CountryCode,
    emailRegex,
    ProviderClientCivility,
    ProviderClientSource,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const providerClientJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'ProviderClient',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        providerClientId: {
            type: 'string',
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        mergedClientId: {
            type: 'string',
            format: 'objectId',
            ref: 'Restaurant',
        },
        source: {
            enum: Object.values(ProviderClientSource),
        },
        firstName: {
            type: 'string',
            trim: true,
            set: capitalize,
        },
        lastName: {
            type: 'string',
            trim: true,
            set: capitalize,
        },
        birthday: {
            type: 'string',
            format: 'date-time',
        },
        address: {
            $ref: '#/definitions/Address',
        },
        email: {
            type: 'string',
            match: emailRegex,
        },
        phone: {
            $ref: '#/definitions/Phone',
        },
        language: {
            enum: [null, ...Object.values(ApplicationLanguage)],
        },
        civility: {
            enum: [null, ...Object.values(ProviderClientCivility)],
        },
        contactOptions: {
            type: 'array',
            items: {
                enum: Object.values(ContactMode),
            },
        },
        visits: {
            type: 'array',
            items: {
                $ref: '#/definitions/Visit',
            },
            default: [],
        },
    },
    required: ['_id', 'providerClientId', 'restaurantId', 'source', 'visits'],
    definitions: {
        Address: {
            type: 'object',
            additionalProperties: false,
            properties: {
                administrativeArea: {
                    type: 'string',
                    nullable: true,
                },
                locality: {
                    type: 'string',
                    nullable: true,
                },
                postalCode: {
                    type: 'string',
                    nullable: true,
                },
                country: {
                    type: 'string',
                },
                formattedAddress: {
                    type: 'string',
                    nullable: true,
                },
                streetNumber: {
                    type: 'string',
                    nullable: true,
                },
                route: {
                    type: 'string',
                    nullable: true,
                },
                regionCode: {
                    enum: Object.values(CountryCode),
                },
            },
            required: ['country', 'regionCode'],
            title: 'Address',
        },
        Phone: {
            type: 'object',
            additionalProperties: false,
            properties: {
                prefix: {
                    type: 'integer',
                },
                digits: {
                    type: 'integer',
                },
            },
            required: ['digits', 'prefix'],
            title: 'Phone',
        },
        Visit: {
            type: 'object',
            additionalProperties: false,
            properties: {
                providerVisitId: {
                    type: 'string',
                },
                restaurantId: {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Restaurant',
                },
                visitDate: {
                    type: 'string',
                    format: 'date-time',
                },
                providerVisitFields: {
                    type: 'object',
                    properties: {},
                    additionalProperties: true,
                },
            },
            required: ['providerVisitId', 'restaurantId', 'visitDate', 'providerVisitFields'],
            title: 'Visit',
        },
    },
} as const satisfies JSONSchemaExtraProps;
